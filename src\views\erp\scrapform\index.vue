<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd" v-if="hasPermission(554)">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
      <template #expandedRowRender="{ record: packagerecord }">
        <BasicTable @register="registerChildrenTable" :data-source="packagerecord.package" />
      </template>
    </BasicTable>
    <editDrawer @register="registereditDrawer" @success="reload" />
  </div>
</template>
<script setup lang="tsx">
import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table'
import { Button, Form, Textarea } from 'ant-design-vue'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { scrapgetList, scrapsetIsCancel, scrapsetStatus } from '/@/api/erp/scrapform'
import { columns, gbuilderColumns, packercolumns, schemas } from './datas/datas'
import { ref } from 'vue'
import { usePermission } from '/@/hooks/web/usePermission'
import { BasicForm, useForm } from '/@/components/Form'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const FormItem = Form.Item
const cancel_remark = ref('')
const { hasPermission } = usePermission()
const formRef = ref<any>()

const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [registerTable, { reload }] = useTable({
  title: '报废单',
  showIndexColumn: false,
  api: scrapgetList,
  columns,
  actionColumn: {
    width: 350,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  },
  rowKey: 'id',
  useSearchForm: true,
  showTableSetting: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,

    fieldMapToTime: [['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]],
    schemas: schemas
  }
})

const [registerChildrenTable] = useTable({
  showIndexColumn: false,
  useSearchForm: false,
  showTableSetting: false,
  canResize: false,
  maxHeight: 400,
  columns: packercolumns
})
//新增
function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增', showFooter: true })
}

function createActions(record: Recordable): ActionItem[] {
  let buttonList: ActionItem[] = [
    {
      label: '确认',
      popConfirm: {
        title: '确认后将无法编辑，且无法编辑，是否确定执行？',
        placement: 'left',
        confirm: handleclearStatus.bind(null, record, 1),
        disabled: record.status !== 0 || record.is_cancel !== 0
      },
      ifShow: hasPermission([555])
    },
    {
      color: 'error',
      label: '仓库审核',
      // disabled: record.status !== 0,
      popConfirm: {
        title: (
          <div>
            <div>确认后将无法编辑，且无法编辑，是否确定执行？</div>
            <div>如若驳回,请输入驳回备注</div>
            <div>
              <BasicForm
                ref={(el: any) => (formRef.value = el?.formActionType)}
                register={useForm}
                showActionButtonGroup={false}
                schemas={gbuilderColumns}
                baseColProps={{ span: 24 }}
              />
            </div>
          </div>
        ),
        placement: 'left',
        confirm: handleclearStatus.bind(null, record, 2),
        disabled: record.status !== 1
      },
      ifShow: hasPermission([556])
    },
    {
      color: 'error',
      label: '财务审核',
      // disabled: record.status !== 0,
      popConfirm: {
        title: (
          <div>
            <div>确认后将无法编辑，且无法编辑，是否确定执行？</div>
            <div>如若驳回,请输入驳回备注</div>
            <div>
              <BasicForm
                ref={(el: any) => (formRef.value = el?.formActionType)}
                register={useForm}
                showActionButtonGroup={false}
                schemas={gbuilderColumns}
                baseColProps={{ span: 24 }}
              />
            </div>
          </div>
        ),
        placement: 'left',
        confirm: handleclearStatus.bind(null, record, 3),
        disabled: record.status !== 2
      },
      ifShow: hasPermission([557])
    }
  ]

  return buttonList
}

function createDropDownActions(record: Recordable): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record)
      //   ifShow: hasPermission([143])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: ![0, 3, 4].includes(record.status) || record.is_cancel !== 0,
      ifShow: hasPermission([558])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '作废',
      popConfirm: {
        title: (
          <div>
            <Form>
              <FormItem label={'作废备注'} required>
                <Textarea v-model:value={cancel_remark.value} allow-clear />
              </FormItem>
            </Form>
          </div>
        ),
        placement: 'left',
        confirm: handleCancel.bind(null, record),
        disabled: record.status === 15 || record.is_cancel !== 0
      },
      ifShow: hasPermission([559])
    },
    {
      label: '反确认',
      popConfirm: {
        title: '确认后将无法编辑，且无法编辑，是否确定执行？',
        placement: 'left',
        confirm: handleclearStatus.bind(null, record, 4),
        disabled: record.status !== 1 || record.is_cancel !== 0
      },
      ifShow: hasPermission([560])
    }
  ]
}

//详情
function handleDetail(record: Recordable) {
  console.log(record)
  openDrawer(true, { record, type: 'detail' })
  setDrawerProps({ title: '报废单详情', showFooter: false })
}
//编辑
function handleEdit(record: Recordable) {
  openDrawer(true, { record, type: 'edit' })
  setDrawerProps({ title: '编辑报废单', showFooter: true })
}

async function handleclearStatus(record, type) {
  const statuss = ref()
  const remark = ref()
  const formdata = await formRef.value?.validate()
  console.log(formdata)

  switch (type) {
    case 1:
      statuss.value = 1
      break
    case 2:
      statuss.value = formdata.status == 1 ? 2 : 3
      remark.value = formdata.status == 2 ? formdata.remark : null
      break
    case 3:
      statuss.value = formdata.status == 1 ? 15 : 4
      remark.value = formdata.status == 2 ? formdata.remark : null
      break
    case 4:
      statuss.value = 0
      break
  }
  await scrapsetStatus({ id: record.id, status: statuss.value, remark: remark.value })
  await reload()
}

async function handleCancel(record) {
  await scrapsetIsCancel({ id: record.id, cancel_remark: cancel_remark.value })
  await reload()
}
</script>
