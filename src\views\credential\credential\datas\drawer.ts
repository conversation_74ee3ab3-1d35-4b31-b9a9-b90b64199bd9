import { ref } from 'vue'
import { getStaffList } from '/@/api/baseData/staff'
import type { BasicColumn, FormSchema } from '/@/components/Table'
import { mapType, mapWorkListFn } from './modal'
import { getCategory, getcustomerList } from '/@/api/financialDocuments/otherIncome'

import { getFinancialInformation } from '/@/api/financialDocuments/capitalFlow'
import { ifArr } from './modal'
import { getDept, getDeptParent32 } from '/@/api/erp/systemInfo'
import { corres_type_options } from '/@/views/financialDocuments/otherExpend/datas/drawer'
import { isNullOrUnDef } from '/@/utils/is'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { getErpSupplier, getWorkList } from '/@/api/commonUtils'
import { cloneDeep } from 'lodash-es'

export const categoryRef = ref()
export const currentEditKeyRef = ref('')

export const propsToKeep = {
  type: true,
  category: true,
  category_id: true,
  amount0: true,
  amount1: true,
  dept_id: true,
  capital: true,
  corres_pondent: true,
  remark: true,
  business: true,
  strid: true,
  date: true,
  corres_type: true,
  is_enter: true,
  clear_dept_id: true,
  sales_strid: true,
  order_number: true,
  files: true
  // 需要保留的其他属性...
}
export const corresType = [
  { label: '员工', value: 1 },
  { label: '部门', value: 2 },
  { label: '客户', value: 3 },
  { label: '供应商', value: 4 }
]
function getWorkListFn(type) {
  if (!type) {
    return getCreatorList
  }
  if (type == 1) {
    return getCreatorList
  }
  if (type == 2) {
    return getDept
  }
  if (type == 3) {
    return getcustomerList
  }
  if (type == 4) {
    return getErpSupplier
  }
}
export const schemas: FormSchema[] = [
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    componentProps: {
      options: Object.keys(mapType).map((item) => ({ label: mapType[item].label, value: item }))
    }
  }
]

export const batchSchemas: FormSchema[] = [
  {
    label: '凭证类别',
    field: 'type',
    required: true,
    component: 'Select',
    componentProps: {
      options: [
        {
          value: 1,
          label: '库存调拨'
        }
      ]
    }
  },
  {
    field: 'creator',
    label: '创建人',
    required: true,
    component: 'ApiSelect',
    componentProps: ({ formActionType }) => ({
      api: getStaffList,
      resultField: 'items',
      selectProps: {
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      },
      params: {
        pageSize: 9999
      },
      onChange: async () => {
        try {
          await formActionType?.validateFields!(['creator'])
        } catch (e) {
          throw new Error(`${e}`)
        }
      }
    }),
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'detail',
    label: '记账明细',
    slot: 'detailSlot',
    component: 'Input',
    colProps: {
      span: 24
    }
  }
]

export const detailColumns: BasicColumn[] = [
  {
    dataIndex: 'remark',
    title: '备注',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'number',
    title: '科目代码',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'category',
    title: '科目名称',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'amount0',
    title: '借方金额(分)',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'amount1',
    title: '贷方金额(分)',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'strid',
    title: '销售订单号',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'department_name',
    title: '部门',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'date',
    title: '日期',
    width: 120,
    resizable: true
  }
]

export const batchInputSchemas: FormSchema[] = [
  {
    field: 'date',
    label: '日期',
    component: 'DatePicker',
    required: true,
    componentProps: {
      style: {
        width: '100%'
      },
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD'
    }
  },
  {
    field: 'order_number',
    label: '凭证单号',
    component: 'Input',
    required: true,
    dynamicDisabled: true
  },
  {
    field: 'files',
    label: '附件',
    component: 'Upload',
    slot: 'Files'
  }
]

export const getDetailColumns: (hand: Function, fnMap?: any) => BasicColumn[] = (hand, fnMap) => [
  {
    dataIndex: 'category',
    title: '科目名称',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'ApiSelect',
    editComponentProps: {
      api: getCategory,
      resultField: 'items',
      selectProps: {
        showSearch: true,
        allowClear: true,
        placeholder: '请选择',
        // labelInValue: true,
        fieldNames: { account_code: 'account_code', value: 'account_name', label: 'account_name' },
        optionFilterProp: 'account_name',
        onChange: (value, shall) => {
          categoryRef.value = value

          if (shall?.account_code && fnMap?.updateTableDataRecord)
            fnMap?.updateTableDataRecord(currentEditKeyRef.value, { category_id: shall.account_code })
        }
      }
    },
    // 默认必填校验
    editRule: true
  },
  {
    dataIndex: 'category_id',
    title: '科目代码',
    width: 80,
    editRow: true,
    editComponent: 'Input',
    editDynamicDisabled: true,
    editRule: true,
    resizable: true
  },
  {
    dataIndex: 'sales_strid',
    title: '销售订单',
    width: 200,
    editRow: true,
    editComponent: 'PagingApiSelect',
    resizable: true,
    editComponentProps: ({ record }) => {
      return {
        resultField: 'items',
        api: getWorkList,
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        searchParamField: 'source_uniqid',
        selectProps: {
          allowClear: true,
          fieldNames: { value: 'source_uniqid', label: 'source_uniqid' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'source_uniqid'
        },
        params: {
          type: 3
        },
        onChange: async (val: number, shall) => {
          record.dept_id = shall.dept_id
          record.dept_name = shall.department_name
          record.business = shall.creator_name
          record.clear_dept_id = shall.operation || undefined
          record.operation_department = shall.operation_department || undefined
        }
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    dataIndex: 'amount0',
    title: '借方金额(分)',
    width: 150,
    resizable: true,
    editRow: true,
    editRule: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      onInput(value) {
        if (value !== 0 && fnMap?.updateTableDataRecord) fnMap?.updateTableDataRecord(currentEditKeyRef.value, { amount1: 0 })
      }
    },
    editDynamicDisabled(record) {
      return record.record.amount1 == 0 ? false : true
    }
  },
  {
    dataIndex: 'amount1',
    title: '贷方金额(分)',
    width: 150,
    resizable: true,
    editRow: true,
    editRule: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      onInput(value) {
        if (value !== 0 && fnMap?.updateTableDataRecord) fnMap?.updateTableDataRecord(currentEditKeyRef.value, { amount0: 0 })
      }
    },
    editDynamicDisabled(record) {
      return record.record.amount0 == 0 ? false : true
    }
  },
  {
    dataIndex: 'dept_name',
    title: '部门',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'PagingApiSelect',
    editComponentProps: {
      api: getDept,
      params: { status: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'name',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'id',
          value: 'name',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true,
        style: {
          width: '100%'
        },
        onChange: (_, shall) => {
          if (fnMap?.updateTableDataRecord) fnMap?.updateTableDataRecord(currentEditKeyRef.value, { dept_id: shall.id })
        }
      }
    }
  },
  {
    dataIndex: 'clear_dept_name',
    title: '结算部门',
    width: 200,
    resizable: true,
    editRow: true,
    // editRule: true,
    editComponent: 'PagingApiSelect',
    editComponentProps: {
      api: getDept,
      params: { is_production: 1, is_audit: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'name',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'id',
          value: 'name',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true,
        style: {
          width: '100%'
        },
        onChange: (_, shall) => {
          if (fnMap?.updateTableDataRecord) fnMap?.updateTableDataRecord(currentEditKeyRef.value, { dept_id: shall.id })
        }
      }
    }
  },
  {
    dataIndex: 'clear_dept_name',
    title: '结算部门',
    width: 200,
    resizable: true,
    editRow: true,
    // editRule: true,
    editComponent: 'PagingApiSelect',
    editComponentProps: {
      api: getDeptParent32,
      params: { status: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'name',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'id',
          value: 'name',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true,
        style: {
          width: '100%'
        },
        onChange: (_, shall) => {
          if (fnMap?.updateTableDataRecord) fnMap?.updateTableDataRecord(currentEditKeyRef.value, { dept_id: shall.id })
        }
      }
    }
  },
  {
    dataIndex: 'clear_dept_id',
    title: '不显示的结算部门id',
    ifShow: false,
    width: 0
  },
  {
    dataIndex: 'clear_dept_name',
    title: '结算部门',
    width: 200,
    resizable: true,
    editRow: true,
    // editRule: true,
    editComponent: 'PagingApiSelect',
    editComponentProps: {
      api: getDeptParent32,
      params: { status: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'name',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'id',
          value: 'name',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true,
        style: {
          width: '100%'
        },
        onChange: (_, shall) => {
          hand(shall)
        }
      }
    }
  },
  {
    dataIndex: 'type',
    title: '单据类型',
    width: 180,
    resizable: true,
    editRow: true,
    editComponent: 'Select',
    editComponentProps: {
      options: Object.keys(mapType).map((item) => ({ label: mapType[item].label, value: item }))
    }
  },
  // {
  //   dataIndex: 'business',
  //   title: '业务对象',
  //   width: 200,
  //   editRow: true,
  //   editComponent: 'Input',
  //   resizable: true
  // },
  {
    dataIndex: 'corres_type',
    title: '往来单位类型',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'Select',
    editComponentProps: ({ record }) => ({
      allowClear: true,
      options: corres_type_options,
      style: {
        width: '100%'
      },
      onChange: (_, shall) => {
        record.corres_pondent = null
        hand(shall)
      }
    })
  },
  {
    dataIndex: 'corres_pondent',
    title: '往来单位',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'PagingApiSelect',
    editComponentProps: ({ record }) => ({
      api: isNullOrUnDef(record.corres_type) ? getCreatorList : mapWorkListFn[record.corres_type],
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 10,
      params: {
        type: 3,
        status: [1, 3, 4, 5, 15]
      },
      selectProps: {
        required: !isNullOrUnDef(record.corres_type), //在这里写有没有效果的
        disabled: isNullOrUnDef(record.corres_type),
        fieldNames: {
          value: 'name',
          label: 'name'
        },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true,
        style: {
          width: '100%'
        },
        onChange: (_, shall) => {
          hand(shall)
        }
      }
    })
  },

  {
    dataIndex: 'capital',
    title: '资金来源',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'ApiSelect',
    editRule: async (text) => {
      const isContained = ifArr.some((item) => categoryRef.value.includes(item))
      //校验通过的情况
      if (isContained && !text) {
        return '请选择资金来源'
      } else {
        return ''
      }
    },
    editComponentProps: {
      api: getFinancialInformation,
      resultField: 'items',
      selectProps: {
        style: { width: '100%' },
        showSearch: true,
        placeholder: '请选择',
        fieldNames: { value: 'name', label: 'name' },
        allowClear: true,
        optionFilterProp: 'name'
      }
    }
  },
  {
    dataIndex: 'remark',
    title: '备注',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'Textarea'
  }
]
export function editschema(type?: boolean): FormSchema[] {
  return [
    {
      field: 'date',
      label: '日期',
      component: 'DatePicker',
      required: true,
      componentProps: {
        style: {
          width: '100%'
        },
        showTime: false,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD'
      }
    },
    {
      field: 'type',
      label: '类型',
      component: 'Select',
      componentProps: {
        options: Object.keys(mapType).map((item) => ({ label: mapType[item].label, value: Number(item) })),
        disabled: true
      },
      required: true,
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'sales_strid',
      label: '关联销售订单',
      component: 'PagingApiSelect',
      componentProps: ({ formModel }) => {
        return {
          resultField: 'items',
          api: getWorkList,
          searchMode: true,
          pagingMode: true,
          pagingSize: 20,
          searchParamField: 'source_uniqid',
          selectProps: {
            allowClear: true,
            fieldNames: { value: 'source_uniqid', label: 'source_uniqid' },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'source_uniqid',
            disabled: type
          },
          params: {
            type: 3
          },
          onChange: async (val: number, shall) => {
            console.log(shall)
            if (!shall) return
            formModel.dept_id = shall.dept_id
            formModel.department = shall.department_name
            formModel.business = shall.creator_name
            formModel.clear_dept_id = shall.operation || null
            formModel.clear_department = shall.clear_department || null
            formModel.operation_department = shall.operation_department || null
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'amount0',
      label: '借方金额',
      component: 'InputNumber',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          precision: 2,
          disabled: formModel.amount1 !== 0 ? true : false
        }
      }
    },
    {
      field: 'amount1',
      label: '贷方金额',
      component: 'InputNumber',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          precision: 2,
          disabled: formModel.amount0 !== 0 ? true : false
        }
      }
    },
    {
      field: 'category',
      label: '支出科目',
      component: 'PagingApiSelect',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          api: getCategory,
          resultField: 'items',
          labelField: 'account_name',
          valueField: 'account_name',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'account_name',
              label: 'account_name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'account_name',
            allowClear: true
          },
          params: {
            status: 1
          },
          onChange: (val: number, shall) => {
            if (!shall) return
            if (shall) {
              formModel.category_id = shall.account_code
            }
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'category_id',
      label: '科目代码',
      component: 'InputNumber',
      required: true,
      componentProps: {
        disabled: true
      }
    },
    {
      field: 'department',
      label: '部门',
      required: true,
      component: 'PagingApiSelect',
      itemProps: {
        validateTrigger: 'blur'
      },
      componentProps: ({ formModel }) => {
        return {
          api: getDept,
          params: { status: 1 },
          resultField: 'items',
          labelField: 'name',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          returnParamsField: 'id',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'id',
              label: 'name'
            },
            disabled: type,
            showSearch: true,
            placeholder: '请选择',
            allowClear: true,
            style: {
              width: '100%'
            }
          },
          onChange: (_, shall) => {
            if (!shall) return
            formModel.dept_id = shall.id
          }
        }
      }
    },
    {
      field: 'clear_department',
      label: '结算部门',
      component: 'PagingApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: getDept,
          params: { is_production: 1, is_audit: 1 },
          resultField: 'items',
          labelField: 'name',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'id',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            allowClear: true,
            style: {
              width: '100%'
            },
            onChange: (_, shall) => {
              formModel.clear_dept_id = shall ? shall.id : null
            }
          }
        }
      }
    },
    {
      field: 'capital',
      label: '资金资料',
      component: 'Input'
    },
    // {
    //   field: 'business',
    //   label: '业务对象',
    //   component: 'Input',
    //   componentProps: {
    //     disabled: type
    //   }
    // },
    {
      label: '往来单位类型',
      field: 'corres_type',
      component: 'Select',
      componentProps: ({ formModel }) => {
        return {
          allowClear: true,
          options: corresType,
          style: {
            width: '100%'
          },
          onChange: (val) => {
            console.log(val)
            if (val == undefined) return (formModel.corres_pondent = undefined)
          }
        }
      }
    },
    {
      label: '往来单位',
      field: 'corres_pondent',
      component: 'PagingApiSelect',
      componentProps: (fromat) => {
        return {
          api: getWorkListFn(fromat.formModel.corres_type),
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          params: {
            type: 3,
            status: [1, 3, 4, 5, 15]
          },
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            style: {
              width: '100%'
            }
          }
        }
      }
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea'
    },
    {
      field: 'files',
      label: '附件',
      component: 'Upload',
      slot: 'Files'
    },
    {
      field: 'dept_id',
      label: '不显示的部门id',
      component: 'Input',
      show: false
    },
    {
      field: 'clear_dept_id',
      label: '不显示的结算部门id',
      component: 'Input',
      show: false
    }
  ]
}

export const childrenColumns = (hand: Function, fnMap?: any): BasicColumn[] => {
  const newDetailsColumns: Array<any> = []
  const datas = cloneDeep(getDetailColumns(hand, fnMap))
  for (const item of datas) {
    if (item.dataIndex == 'corres_pondent') {
      item.editComponent = 'Input'
      item.editComponentProps = {}
    }
    newDetailsColumns.push(item)
  }
  return newDetailsColumns
}
