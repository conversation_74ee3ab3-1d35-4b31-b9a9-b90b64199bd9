<template>
  <div>
    <BasicTable @register="registerTable" :data-cachekey="routePath">
      <template #toolbar>
        <!--<Button :disabled="generateBtnStatus" class="mr-8px" @click="handleCreatePayment" v-if="hasPermission([168])">生成付款单 </Button>-->
        <!--<Button :disabled="generateGenerate" @click="handleCreateReceipt" class="mr-8px" v-if="hasPermission([169])">生成收款单 </Button>-->
        <Button type="primary" @click="handleCreate" v-if="hasPermission([163])">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
    </BasicTable>

    <RefundDrawer @register="registerDrawer" @success="handleSuccess" />
    <GenerateModal @register="registerModal" @success="handleSuccess" />
    <PaymentModal @register="registerPayModal" @success="handleSuccess" />
  </div>
</template>

<script setup lang="ts" name="/financialDocuments/refund">
import { ref } from 'vue'
import { message, Button } from 'ant-design-vue'
import { useTable, BasicTable, TableAction, ActionItem } from '/@/components/Table'
import { delFund, getFundManage, setFundStatus } from '/@/api/financialDocuments/refund'
import { useMessage } from '/@/hooks/web/useMessage'
import { useDrawer } from '/@/components/Drawer'
import RefundDrawer from './components/refundDrawer.vue'
import { fundManageColumns, searchFormSchema } from './datas/datas'
import GenerateModal from './components/GenerateModal.vue'
import PaymentModal from './components/PaymentModal.vue'
import { useModal } from '/@/components/Modal'
import { usePermission } from '/@/hooks/web/usePermission'
import { isAllEqual } from '../../erp/purchaseOrder/datas/datas'
import { paymentList } from './datas/fn'
import { useRoute } from 'vue-router'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const route = useRoute()
const { path: routePath } = route
const { hasPermission } = usePermission()
const { createMessage } = useMessage()

// const generateBtnStatus = ref(true)
// const generateGenerate = ref(true)
//tabs切换
const show = ref(false)

/** 注册抽屉 */
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()

/** 注册表格 */
const [registerTable, { reload, clearSelectedRowKeys }] = useTable({
  title: '退款单列表',
  api: getFundManage,
  columns: fundManageColumns,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    name: 'searchForm',
    schemas: searchFormSchema,
    fieldMapToTime: [
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['check_at', ['check_at_start', 'check_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  afterFetch: IncomeListafter,
  useSearchForm: true,
  showTableSetting: true,
  tableSetting: {
    redo: true,
    size: true,
    setting: false,
    fullScreen: true
  },
  rowKey: 'id',
  showIndexColumn: false,
  actionColumn: {
    width: 190,
    title: '操作',
    dataIndex: 'action'
  },
  pagination: {
    pageSize: 10,
    position: ['bottomRight']
  }
  // rowSelection: {
  //   getCheckboxProps: (record: IRecord) => {
  //     return { disabled: record.status !== 1 || record.type === 2 }
  //   },
  //   onChange: handleTableSelectChange
  // },
  // rowSelection: {
  //   type: 'checkbox',
  //   onChange: handleChange,
  //   getCheckboxProps: (record) => {
  //     if (record.status !== 1 || record.type !== 1) {
  //       return { disabled: true }
  //     } else {
  //       return { disabled: false }
  //     }
  //   }
  // }
})

/** 注册modal */
const [registerModal, { openModal }] = useModal()

const [registerPayModal, { openModal: openPayModal }] = useModal()

function createActions(record): ActionItem[] {
  return [
    {
      icon: 'ant-design:file-search-outlined',
      label: '审核',
      popConfirm: {
        title: '审核后将无法撤回，且无法编辑，是否确定审核？',
        placement: 'left',
        confirm: handleDecision.bind(null, record)
      },
      disabled: record.status !== 0,
      ifShow: hasPermission([166])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: record.status !== 0 && record.is_check2 !== 2,
      ifShow: hasPermission([165])
    }
  ]
}

function createDropDownActions(record): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([164])
    },
    {
      icon: 'ant-design:delete-outlined',
      label: '删除',
      color: 'error',
      popConfirm: {
        title: '是否确定删除？',
        placement: 'left',
        confirm: handleDelete.bind(null, record),
        disabled: record.status !== 0 || record.is_check2 !== 0
      },
      // disabled: record.status === 1,
      ifShow: hasPermission([167])
    }
  ]
}

/** 新增 */
function handleCreate() {
  paymentList.value = []
  setDrawerProps({ title: '新增退款单' })
  openDrawer(true, { type: 'add' })
}

/** 编辑 */
function handleEdit(record) {
  setDrawerProps({ title: '更新退款单' })
  openDrawer(true, { type: 'edit', record })
}

/** 详情 */
function handleDetail(record) {
  setDrawerProps({ title: '退款单详情' })
  openDrawer(true, { type: 'detail', record })
}

/** 审批 */
async function handleApprove(record) {
  try {
    await setFundStatus(record.id)
    createMessage.success('审批成功！')
    reload()
  } catch (e) {
    createMessage.error('审批失败！')
    throw new Error(`${e}`)
  }
}

/** 删除 */
async function handleDelete(record) {
  try {
    await delFund(record.id)
    createMessage.success('删除成功！')
    reload()
  } catch (e) {
    createMessage.error('删除失败！')
    throw new Error(`${e}`)
  }
}

/** 生成收款单 */
// async function handleCreateReceipt() {
async function handleCreateReceipt(record) {
  const show = ref(false)
  // const selectedRow = await getSelectRows()
  const selectedRow = [record]
  const result = selectedRow.filter((item) => item.order === 2)
  if (result.length === 0) return message.warn('请选择至少一条采购退款单!')

  if (new Set(result.map((item) => item.supplier_id)).size !== 1) return message.warn('请选择相同供应商的采购退款单!')
  for (let item of selectedRow) {
    if (item.payment_type && item.payment_type !== 1) {
      message.warning('该采购订单已完成付款单最后一笔款或全款生成')
      show.value = true
      break
    }
  }
  if (!show.value) {
    openModal(true, { selectRowsData: selectedRow })
  }
}

/** 生成付款单 */
// async function handleCreatePayment() {
async function handleCreatePayment(record) {
  //如果选中的数据,并且都是销售退款单,而且客户相同
  // const selectedRow = await getSelectRows()
  const selectedRow = [record]
  const result = selectedRow.filter((item) => item.order === 1)
  if (result.length === 0) return message.warn('请选择至少一条销售退款单')
  if (!isAllEqual(selectedRow, 'client_id')) return message.error('不同供应商或不同部门不能生成同一张付款单！')
  for (let item of selectedRow) {
    if (item.payment_type && item.payment_type !== 1) {
      message.warning('该采购订单已完成付款单最后一笔款或全款生成')
      show.value = true
      break
    }
  }
  if (!show.value) {
    openPayModal(true, { selectedRow })
  }
}

/** 选中 */
// function handleChange() {
//   const result = getSelectRows()
//   if (result.length !== 0) {
//     const isAllOrder1 = result.every((item) => item.order == 1)
//     const isAllOrder2 = result.every((item) => item.order == 2)
//
//     generateBtnStatus.value = !isAllOrder1
//     generateGenerate.value = !isAllOrder2
//   } else {
//     generateBtnStatus.value = true
//     generateGenerate.value = true
//   }
// }

function handleSuccess() {
  reload()
}
//数据处理
function IncomeListafter(data) {
  clearSelectedRowKeys()
  return data.client_name
}

// 根绝type不同调用不同的函数
function handleDecision(record) {
  const { type } = record
  const mapTypeFn = {
    1: handleRefundApprove,
    2: handleApprove
  }

  mapTypeFn[type]?.(record)
}

function handleRefundApprove(record) {
  const { order } = record
  const mapFn = {
    1: handleCreatePayment,
    2: handleCreateReceipt
  }
  mapFn[order]?.(record)
}
</script>
<style lang="less" scoped>
.vben-basic-table {
  padding-top: 0 !important;
}
</style>
