import { getCategory } from '/@/api/financialDocuments/otherIncome'
import { BasicColumn, FormSchema } from '/@/components/Table'

export const statustype = {
  0: { label: '未审核', color: 'red' },
  1: { label: '已审核', color: 'green' }
}
export const columns: BasicColumn[] = [
  {
    dataIndex: 'order_no',
    title: '凭证单号',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'strid',
    title: '单据单号',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'status',
    title: '状态',
    width: 150,
    resizable: true
  },
  {
    dataIndex: 'department',
    title: '部门',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'amount0',
    title: '借方金额(元)',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'amount1',
    title: '贷方金额(元)',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'category',
    title: '科目名称',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'year',
    title: '会计年度',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'issue',
    title: '会计期间',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'day',
    title: '天',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'date',
    title: '结转日期',
    width: 120,
    resizable: true
  },
  {
    dataIndex: 'remark',
    title: '备注',
    width: 120,
    resizable: true
  }
]
export const schemas: FormSchema[] = [
  {
    field: 'year',
    label: '结转年度',
    component: 'DatePicker',
    componentProps: {
      picker: 'year',
      valueFormat: 'YYYY'
    }
  },
  {
    field: 'issue',
    label: '结转月份',
    component: 'Select',
    componentProps: {
      options: [
        { label: '一月', value: 1 },
        { label: '二月', value: 2 },
        { label: '三月', value: 3 },
        { label: '四月', value: 4 },
        { label: '五月', value: 5 },
        { label: '六月', value: 6 },
        { label: '七月', value: 7 },
        { label: '八月', value: 8 },
        { label: '九月', value: 9 },
        { label: '十月', value: 10 },
        { label: '十一月', value: 11 },
        { label: '十二月', value: 12 }
      ]
    }
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: [
        { label: '未审核', value: 0 },
        { label: '已审核', value: 1 }
      ]
    }
  },
  {
    field: 'strid',
    label: '单据单号',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    field: 'category',
    label: '科目名称',
    component: 'ApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: {
      api: getCategory,
      resultField: 'items',
      params: { is_profitloss: 1 },
      selectProps: {
        showSearch: true,
        allowClear: true,
        placeholder: '请选择',
        // labelInValue: true,
        fieldNames: { account_code: 'account_code', value: 'account_name', label: 'account_name' },
        optionFilterProp: 'account_name'
      }
    }
  }
]
