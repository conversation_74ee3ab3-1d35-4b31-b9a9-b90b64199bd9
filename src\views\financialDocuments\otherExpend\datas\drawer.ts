import { computed, h, ref } from 'vue'
// import type { DefaultOptionType } from 'ant-design-vue/lib/vc-tree-select/TreeSelect'
import type { BasicColumn, FormSchema } from '/@/components/Table'
import { getCategory, getcustomerList } from '/@/api/financialDocuments/otherIncome'
import { getInchargeList, getDept } from '/@/api/erp/systemInfo'
import { getErpSupplier, getWorkList } from '/@/api/commonUtils'
// import { getDeptSelectTree } from '/@/api/admin/dept'
import { message, Tag } from 'ant-design-vue'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { getSourceSelect } from '/@/api/baseData/warehouse'
import { cloneDeep, isNil } from 'lodash-es'
import { isNullAndUnDef } from '/@/utils/is'
import { mfgetCashierAndOrder, toolsgetPaOrderInfo } from '/@/api/financialDocuments/otherExpend'
import { cpgetList } from '/@/api/erp/purchaseOrder'
import { mul } from '/@/utils/math'
import { getRmbquot } from '/@/api/erp/sales'
import Decimal from 'decimal.js'
import { getProjectList } from '/@/api/projectOverview'
// import { getDeptSelectTree } from '/@/api/admin/dept'
const pathname = window.location.pathname
//结算货币假数据
// const currenyData: any = [
//   { value: 'CNY', label: 'CNY' },
//   { value: 'USD', label: 'USD' }
// ]
const typeOptions: Recordable[] = [
  { label: '款项支出', value: 2 },
  { label: '个人报销', value: 1 }
]
const typeOption: Recordable[] = [
  { label: '款项支出', value: 2 },
  { label: '个人报销', value: 1 },
  { label: '财务费用', value: 3 }
]
const canceltypeOptions = {
  0: { label: '未取消', color: '' },
  1: { label: '取消', color: 'red' }
}

const hasBeenSet = ref(false)

export const corres_type_options = [
  { label: '员工', value: 1 },
  { label: '部门', value: 2 },
  { label: '客户', value: 3 },
  { label: '供应商', value: 4 },
  { label: '其他', value: 5 }
]

export function getWorkListFn(type) {
  if (!type) {
    return getCreatorList
  }
  if (type == 1) {
    return getCreatorList
  }
  if (type == 2) {
    return getDept
  }
  if (type == 3) {
    return getcustomerList
  }
  if (type == 4) {
    return getErpSupplier
  }
}
//是否往来单位必填
const is_need_corres = ref(0)
//是否分摊渠道必填
const is_need_share_channel = ref(0)
//是否分摊人员必填
const is_need_share_person = ref(0)

//单据类型
const expenseType = ref(0)
export const inChargeList = ref<any>([])
export const compAllSalesWorkList = computed(() => {
  const mapList = {}
  for (const sales of inChargeList.value) {
    mapList[sales.id] = sales
  }
  return mapList
})

/**
 * 更新费用详情列
 * @param salesList - 单号数组
 * @param hand - 手动更新回调函数
 * @param type - 编辑/详情
 * @param order - 销售/采购
 * @param check - 驳回
 * @returns 返回费用详情列
 */

export function updataexpenseDetails(salesList: any[], hand?: Function, type?: any, order?: any, check?: any): any {
  const expenseDetailsColumns: BasicColumn[] = [
    {
      title: '关联销售单号',
      dataIndex: 'source_uniqid',
      editComponent: 'PagingApiSelect',
      editComponentProps: ({ record }) => {
        return {
          api: getWorkList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          params: {
            types: [3, 27],
            status: [1, 2, 3, 4, 5, 15]
          },
          // pagingSize: 20,
          searchParamField: 'source_uniqid',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'source_uniqid',
              label: 'source_uniqid'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'source_uniqid',
            allowClear: true,
            onChange: (_, shall) => {
              if (!shall) {
                record.source_uniqid = undefined
              }
              const idx = salesList?.value?.findIndex((item) => {
                return item.id === shall.id
              })
              // salesList缺少选项导致提交缺少work_id
              if (idx === -1) salesList.value?.push(shall)
              hand && hand(shall)
              hasBeenSet.value = false
            },
            style: {
              width: '100%'
            }
          }
        }
      },
      ifShow: order == 2,
      width: 250,
      resizable: true,
      editRow: true,
      editDynamicDisabled: order == 2
    },
    {
      title: order == 2 ? '关联采购单号' : '关联销售单号',
      dataIndex: 'parent_strid',
      editComponent: 'PagingApiSelect',
      editComponentProps: ({ record }) => {
        return {
          api: getWorkList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          pagingSize: 20,
          params: {
            types: order == 2 ? [4] : [3, 27],
            status: [1, 2, 3, 4, 5, 15]
          },
          searchParamField: order == 2 ? 'strid' : 'source_uniqid',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: order === 2 ? 'purchase_strid' : 'source_uniqid',
              label: order === 2 ? 'purchase_strid' : 'source_uniqid'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: order == 2 ? 'purchase_strid' : 'source_uniqid',
            allowClear: true,
            onChange: async (_, shall) => {
              if (!shall) {
                record.parent_strid = undefined
                record.source_uniqid = undefined
                record.par_work_id = undefined
              }
              const idx = salesList?.value?.findIndex((item) => {
                return item.id === shall.id
              })
              // salesList缺少选项导致提交缺少work_id
              if (idx === -1) salesList.value?.push(shall)
              hand && hand(shall)
              hasBeenSet.value = false
              if (order == 2) {
                record.basic_work_id = shall.parent_id
                const { items } = await getWorkList({ id: shall.parent_id })
                record.source_uniqid = items[0].source_uniqid
                hand && hand(items[0])
              }
              expenseType.value = shall.type
              record.par_work_id = shall.id
              record.inCharge = shall.inCharge
              if (!record.order_no) {
                record.department = shall.department_name
                record.dept_id = shall.dept_id
              }
              record.clear_department = shall.clear_department
              record.clear_dept_id = shall.clear_dept_id
            },
            style: {
              width: '100%'
            }
          }
        }
      },
      editDynamicDisabled(record) {
        return record.record.order_no ? true : false
      },
      width: 250,
      ifShow: ![3, 4, 6, 7].includes(order),
      editRow: true,
      resizable: true
    },
    {
      title: 'OA工单号',
      dataIndex: 'order_no',
      helpMessage: 'OA工单号选择之后,将会覆盖选择的销售订单或者采购订单的支出部门,请谨慎选择',
      editComponent: 'PagingApiSelect',
      editComponentProps: ({ record }) => {
        return {
          api: toolsgetPaOrderInfo,
          params: { type: 2 },
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          pagingSize: 20,
          labelField: 'order_no',
          valueField: 'order_no',
          searchParamField: 'order_no',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'order_no',
              label: 'order_no'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'account_name',
            allowClear: true,
            onChange: async (_, shall) => {
              record.dept_id = shall.f_dept_id
              record.department = shall.f_department
            },
            style: {
              width: '100%'
            }
          }
        }
      },
      editDynamicDisabled(record) {
        return record.record.parent_strid ? true : false
      },
      ifShow: order == 3,
      width: 250,
      editRow: true,
      resizable: true
    },
    {
      title: '支出科目名称',
      dataIndex: 'account_name',
      editComponent: 'ApiSelect',
      width: 250,
      helpMessage: '明细具有销售订单时支出科目不能选择营业费及其下属科目用等支出科目,反之不具有销售订单时不可选择其他业务支出及其下属科目',
      editRow: true,
      resizable: true,
      editComponentProps: ({ record }) => {
        return {
          api: getCategory,
          resultField: 'items',
          labelField: 'account_name',
          valueField: 'account_name',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'account_name',
              label: 'account_name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'account_name',
            allowClear: true,
            onChange: async (_, shall) => {
              if (!shall) {
                record.account_name = undefined
                record.account_code = undefined
                is_need_corres.value = 0
                is_need_share_channel.value = 0
                is_need_share_person.value = 0
              }
              record.share_inCharge = undefined
              record.share_source = undefined
              record.corres_type = undefined
              record.corres_pondent = undefined
              is_need_corres.value = shall.is_need_corres
              is_need_share_channel.value = shall.is_need_share_channel
              is_need_share_person.value = shall.is_need_share_person
              hand && hand(shall)
            },
            style: {
              width: '100%'
            }
          },

          params: {
            status: 1
          }
        }
      },
      editRule(text, record) {
        if (!text) return false
        if (!['/car', '/sp', '/sptests'].includes(pathname)) {
          if (record.parent_strid || record.order_no) {
            const dashIndex = text.indexOf('-')
            const beforeDash = text.substring(0, dashIndex)
            if (beforeDash === '营业费用' || beforeDash === '营业费用（旧）') {
              message.error('不能选择营业费用')
              return true
            } else {
              return false
            }
          } else {
            const dashIndex = text.indexOf('-')
            const beforeDash = text.substring(0, dashIndex)
            if (beforeDash === '其他业务支出' || beforeDash === '其他业务支出（ERP）') {
              message.error('不能选择其他业务支出')
              return true
            } else {
              return false
            }
          }
        }
      }
    },
    {
      title: '财务驳回备注',
      dataIndex: 'reject_remark1',
      editComponent: 'Textarea',
      editDynamicDisabled: true,
      resizable: true,
      ifShow: check
    },
    {
      title: '出纳驳回备注',
      dataIndex: 'reject_remark2',
      editComponent: 'Textarea',
      editDynamicDisabled: true,
      resizable: true,
      ifShow: check
    },
    {
      title: '摘要',
      dataIndex: 'desc',
      editComponent: 'Textarea',
      width: 200,
      editRow: true,
      resizable: true,
      editComponentProps: {
        autosize: { minRows: 3, maxRows: 6 },
        style: {
          width: '100%'
        }
      }
    },
    {
      title: '外币金额',
      dataIndex: 'foreign_currency_amount',
      editComponent: 'InputNumber',
      editComponentProps: ({ record }) => {
        return {
          // max: num.value,
          precision: 2,
          step: 0.01,
          style: {
            width: '100%'
          },
          onChange: (val) => {
            record.amount = new Decimal(val).times(record.exchange_rate).toDecimalPlaces(2).toNumber()
          }
        }
      },
      width: 150,
      editRow: true,
      resizable: true,
      defaultHidden: true,
      editDynamicDisabled({ record }) {
        return !['人民币', 'CNY'].includes(record.currency) ? false : true
      }
    },
    {
      title: '支出金额',
      dataIndex: 'amount',
      editComponent: 'InputNumber',
      editComponentProps: () => {
        return {
          // max: num.value,
          precision: 2,
          step: 0.01,
          style: {
            width: '100%'
          }
        }
      },
      width: 150,
      editRow: true,
      resizable: true,
      editDynamicDisabled({ record }) {
        return ['人民币', 'CNY'].includes(record.currency) ? false : true
      }
    },
    {
      title: '支出部门',
      dataIndex: 'department',
      editComponent: 'PagingApiSelect',
      editComponentProps: ({ record }) => {
        return {
          api: getDept,
          params: { status: 1, is_show: 1, is_audit: 1 },
          resultField: 'items',
          // labelField: 'name',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            // optionFilterProp: 'name',
            allowClear: true,
            style: {
              width: '100%'
            },
            onChange(_, shall) {
              if (!shall) return
              record.dept_id = shall.id
            }
          }
        }
      },
      editDynamicDisabled(record) {
        return record.record.parent_strid || record.record.order_no ? true : false
      },
      width: 200,
      editRow: true,
      resizable: true
    },
    {
      title: '结算部门',
      dataIndex: 'clear_department',
      editComponent: 'PagingApiSelect',
      editComponentProps: ({ record }) => {
        return {
          api: getDept,
          params: { is_audit: 1 },
          resultField: 'items',
          labelField: 'name',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            style: {
              width: '100%'
            },
            onChange(_, shall) {
              if (!shall) return
              record.clear_dept_id = shall.id
            }
          }
        }
      },
      editDynamicDisabled: true,
      width: 200,
      editRow: true,
      resizable: true
    },
    {
      title: '是否订单最后一笔支出',
      dataIndex: 'is_last_disburse',
      editComponent: 'Select',
      width: 200,
      editComponentProps: {
        options: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ],
        allowClear: true
      },
      editRow: true,
      customRender({ text }) {
        return isNullAndUnDef(text) ? '-' : text === 1 ? '是' : '否'
      },
      editRule(text) {
        const booleans = [1, 0].includes(text)
        if (expenseType.value == 27 && !booleans) {
          message.error('当前订单必须选择是否订单最后一笔支出')
          return true
        } else {
          return false
        }
      }
    },
    {
      title: '分摊人员',
      dataIndex: 'share_inCharge_name',
      editComponent: 'PagingApiSelect',
      editComponentProps: ({ text }) => {
        return {
          api: getCreatorList,
          params: { status: 1 },
          resultField: 'items',
          labelField: 'name',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          returnParamsField: 'id',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            style: {
              width: '100%'
            },
            disabled: isNil(text) || is_need_share_person.value !== 1,
            onChange: (_, shall) => {
              inChargeList.value.push(shall)
            }
          }
        }
      },
      editRule(text) {
        if (!text && is_need_share_person.value == 1) {
          message.error('当前科目需要填写分摊人员')
          return true
        } else {
          return false
        }
      },
      width: 200,
      editRow: true,
      resizable: true
    },
    {
      title: '分摊渠道',
      dataIndex: 'share_source',
      editComponent: 'PagingApiSelect',
      editComponentProps: ({ text }) => {
        return {
          api: getSourceSelect,
          params: { status: 0 },
          resultField: 'items',
          labelField: 'name',
          valueField: 'id',
          searchMode: true,
          pagingMode: true,
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            style: {
              width: '100%'
            },
            disabled: isNil(text) || is_need_share_person.value !== 1
          }
        }
      },
      editRule(text) {
        if (!text && is_need_share_channel.value == 1) {
          message.error('当前科目需要填写分摊渠道')
          return true
        } else {
          return false
        }
      },
      width: 200,
      editRow: true,
      resizable: true
    },
    {
      title: '往来单位类型',
      dataIndex: 'corres_type',
      editComponent: 'Select',
      editComponentProps: ({ record }) => {
        return {
          allowClear: true,
          options: corres_type_options,
          style: {
            width: '100%'
          },
          onChange: (val, shall) => {
            record.corres_pondent = null
            hand && hand(shall)
          }
        }
      },
      editRule(text) {
        if (!text && is_need_corres.value == 1) {
          message.error('当前科目需要填写往来单位与对应类型')
          return true
        } else {
          return false
        }
      },
      width: 200,
      editRow: true,
      resizable: true
    },
    {
      title: '往来单位',
      dataIndex: 'corres_pondent',
      editComponent: 'PagingApiSelect',
      editComponentProps: (fromat) => {
        return {
          api: getWorkListFn(fromat.record.corres_type),
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          params: {
            type: 3,
            status: [1, 3, 4, 5, 15]
          },
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'name',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            style: {
              width: '100%'
            }
          }
        }
      },
      editRule(text: string) {
        return Promise.resolve(is_need_corres.value === 1 && !text ? '当前科目需要填写往来单位' : '')
      },
      width: 200,
      resizable: true,
      editRow: true
      // ifShow: is_need_corres_type.value === 5 ? true : false
      // defaultHidden:
    },
    // {
    //   title: '往来单位',
    //   dataIndex: 'corres_pondent',
    //   editComponent: 'Input',
    //   width: 200,
    //   resizable: true,
    //   editRow: true,
    //   editRule(text: string) {
    //     return Promise.resolve(is_need_corres.value === 1 && !text ? '当前科目需要填写往来单位' : '')
    //   },
    //   ifShow: is_need_corres_type.value !== 5 ? true : false
    //   // defaultHidden:
    // },
    {
      title: '明细单号',
      dataIndex: 'strid',
      width: 200,
      ifShow: ['edit', 'detail'].includes(type),
      resizable: true
    },
    {
      title: '支出科目代码',
      dataIndex: 'account_code',
      width: 200,
      editRow: true,
      editComponent: 'Input',
      editDynamicDisabled: true,
      resizable: true
    },
    {
      title: '汇率',
      dataIndex: 'exchange_rate',
      width: 100,
      editRow: true,
      resizable: true,
      editComponent: 'Input',
      editDynamicDisabled: true
    },
    {
      title: '货币',
      dataIndex: 'currency',
      width: 100,
      // editRule: RouleFn,
      editRow: true,
      editComponent: 'Input',
      editDynamicDisabled: true,
      resizable: true
    },
    {
      title: '财务审核',
      dataIndex: 'is_check',
      width: 100,
      ifShow: ['detail', 'edit'].includes(type),
      resizable: true
    },
    {
      title: '出纳审核',
      dataIndex: 'is_check2',
      width: 100,
      ifShow: ['detail', 'edit'].includes(type),
      resizable: true
    },
    {
      title: '是否取消',
      dataIndex: 'is_cancel',
      width: 100,
      customRender: ({ value }) => {
        return value ? h(Tag, { color: canceltypeOptions[value].color }, canceltypeOptions[value].label) : '-'
      },
      resizable: true
    },
    {
      title: '取消时间',
      dataIndex: 'cancel_at',
      width: 150,
      customRender: ({ value }) => {
        // return value ? value.split(' ')[0].slice(0, 10) : '-'
        return value ? value : '-'
      },
      resizable: true
    },
    {
      title: '附件',
      dataIndex: 'files',
      width: 100,
      resizable: true
    },
    {
      title: 'id',
      dataIndex: 'id',
      width: 100,
      defaultHidden: true,
      resizable: true
    },
    {
      title: 'parent_id',
      dataIndex: 'parent_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'share_inCharge',
      dataIndex: 'share_inCharge',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'dept_id',
      dataIndex: 'dept_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'clear_dept_id',
      dataIndex: 'clear_dept_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'parent_id',
      dataIndex: 'parent_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'par_work_id',
      dataIndex: 'par_work_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'basic_work_id',
      dataIndex: 'basic_work_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'work_id',
      dataIndex: 'work_id',
      width: 100,
      defaultHidden: true
    },
    {
      title: 'inCharge',
      dataIndex: 'inCharge',
      width: 100,
      defaultHidden: true
    }
  ]
  return expenseDetailsColumns
}
export async function updateFormSchemaFn(status: number, paramtype: boolean, type: string) {
  const updateFormSchema: FormSchema[] = [
    {
      field: 'order',
      label: '订单类型',
      component: 'Select',
      required: true,
      componentProps: ({ formModel }) => ({
        options: [
          { label: '销售订单', value: 1 },
          { label: '采购订单', value: 2 },
          { label: 'OA工单号', value: 3 },
          { label: '费用报销(无发票)', value: 4 },
          { label: '餐厅报销', value: 5 },
          { label: '费用报销(有发票)', value: 6 },
          { label: '财务费用支出', value: 7 }
        ],
        onChange(value) {
          if (value == 4) {
            formModel.type = 1
          }
          if (value == 6) {
            formModel.type = 2
          }
          if (value == 5) {
            formModel.exchange_rate = '1.000000'
            formModel.currency = '人民币'
            formModel.rate = '1.000000'
          }
        }
      })
    },
    {
      field: 'is_dept_batch',
      label: '按部门批量生成',
      defaultValue: 0,
      itemHelpMessage: '这功能用于明细支出涉及多个项目时需要多个项目负责人的部门主管审核时候使用',
      component: 'RadioButtonGroup',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 0 }
          ],
          onChange: (value) => {
            if (value == 1) {
              formModel.inCharge = undefined
              formModel.doc_source_uniqid = undefined
              formModel.dept_id = undefined
              formModel.sales_work_id = undefined
            }
          }
        }
      },
      show({ model }) {
        return model.order == 1 && type == 'add'
      },
      ifShow({ model }) {
        return model.order == 1 && type == 'add'
      }
    },
    {
      field: 'sales_work_id',
      label: 'sales_work_id',
      component: 'Input',
      defaultValue: null,
      ifShow: true,
      show: false
    },
    {
      field: 'doc_source_uniqid',
      label: '项目负责人相关订单号',
      component: 'PagingApiSelect',
      itemHelpMessage: '本功能是快捷带出订单的项目负责人的部门和其主管，如果本单据审批不需要项目负责人所在部门主管审核请不要填写。',
      componentProps: ({ formModel }) => {
        return {
          api: getWorkList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          params: {
            types: [3],
            status: [1, 2, 3, 4, 5, 15]
          },
          pagingSize: 20,
          searchParamField: 'source_uniqid',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'source_uniqid',
              label: 'source_uniqid'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'source_uniqid',
            allowClear: true,
            onChange: (_, shall) => {
              console.log(_, shall)
              if (!shall) {
                console.log('123')
                formModel.dept_id = null
                formModel.inCharge = null
                formModel.sales_work_id = undefined
              } else {
                formModel.dept_id = shall?.inCharge_dept_id
                formModel.inCharge = shall.inCharge_dept_leader_id
                formModel.sales_work_id = shall.id
              }
            },
            style: {
              width: '100%'
            }
          }
        }
      },
      dynamicDisabled({ model }) {
        return model.is_dept_batch == 1
      }
    },
    {
      field: 'urgent_level',
      label: '紧急程度',
      component: 'Select',
      componentProps: {
        options: [
          { label: '一般', value: 1 },
          { label: '紧急', value: 2 }
        ]
      },
      required({ model }) {
        return model.order !== 4 ? true : false
      },
      show({ model }) {
        return model.order !== 4 ? true : false
      }
    },
    {
      field: 'contracting_party',
      label: '我司签约主体',
      component: 'PagingApiSelect',
      componentProps: ({ formModel }) => ({
        api: cpgetList,
        resultField: 'items',
        disabledField: 'name',
        disabledOptions: formModel.order === 6 ? ['非对公账户'] : [],
        selectProps: {
          fieldNames: { key: 'key', value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          optionFilterProp: 'name'
        }
      }),
      itemProps: {
        validateTrigger: 'blur'
      },
      required({ model }) {
        return model.order !== 4 ? true : false
      },
      show({ model }) {
        return model.order !== 4 ? true : false
      },
      dynamicDisabled({ model }) {
        return model.type == 1 && model.order == 4 ? true : false
      }
    },
    {
      field: 'collection_at',
      label: '付款日期',
      component: 'DatePicker',
      required: true,
      componentProps: {
        valueFormat: 'YYYY-MM-DD'
      },
      ifShow: paramtype,
      show: paramtype
    },
    {
      field: 'type',
      label: '类型',
      component: 'Select',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          options: paramtype ? typeOption : status == 0 ? typeOptions : [{ label: '财务费用', value: 3 }],
          onChange: (value) => {
            if (value == 1) {
              formModel.contracting_party = '非对公账户'
            }
          }
        }
      },
      dynamicDisabled({ model }) {
        return model.order == 4 || model.order == 6 ? true : false
      }
    },
    {
      field: 'currency',
      label: '结算货币',
      component: 'Input',
      required: true,
      defaultValue: '人民币',
      componentProps: {
        disabled: true
      }
    },
    {
      field: 'rate',
      label: '汇率',
      component: 'ApiSelect',
      defaultValue: '1.000000',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          api: getRmbquot,
          resultField: 'items',
          selectProps: {
            fieldNames: { value: 'fBuyPri', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            onChange(val, shall) {
              if (val == '1.000000') {
                formModel.fg_amount = undefined
                formModel.total_price = undefined
              }
              formModel.exchange_rate = val
              formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
              formModel.currency = shall.name.split('-')[0]
            },
            disabled: formModel.order == 5 ? true : false
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: 'exchange_rate',
      label: '汇率比例',
      component: 'InputNumber',
      componentProps: ({ formModel }) => {
        return {
          precision: 4,
          min: 0,
          max: 100,
          onChange(val) {
            formModel.total_price = mul(val, formModel.fg_amount || 0, 2)
          },
          disabled: formModel.order == 5 ? true : false
        }
      },
      defaultValue: '1.000000'
    },
    {
      field: 'foreign_currency_amount',
      label: '外汇金额总额',
      component: 'InputNumber',
      defaultValue: 0,
      componentProps: ({}) => ({
        disabled: true,
        precision: 4,
        min: 0
      })
    },
    {
      field: 'cost',
      label: '应付金额',
      component: 'InputNumber',
      defaultValue: 0,
      componentProps: ({}) => ({
        disabled: true,
        precision: 4,
        min: 0
      }),
      required: true
    },
    {
      field: 'dept_id',
      label: '部门',
      // component: 'ApiTreeSelect',
      // componentProps: {
      //   api: getDeptSelectTree,
      //   immediate: false,
      //   lazyLoad: true,
      //   treeSelectProps: {
      //     fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
      //     placeholder: '请选择',
      //     showSearch: true,
      //     optionFilterProp: 'name',
      //     treeDefaultExpandAll: true,
      //     filterTreeNode: (search, item) => {
      //       if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
      //       return false
      //     }
      //   }
      // },
      component: 'ApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: getDept,
          params: { status: 1, is_show: 1 },
          resultField: 'items',
          labelField: 'name',
          valueField: 'id',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'id',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            disabled: formModel.is_dept_batch == 1 || formModel.sales_work_id ? true : false
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      required({ model }) {
        return model.is_dept_batch == 1 ? false : true
      },
      dynamicDisabled({ model }) {
        return model.is_dept_batch == 1 ? true : false
      }
    },

    {
      field: 'applicant',
      label: '申请人',
      component: 'ApiSelect',
      componentProps: () => {
        return {
          api: getInchargeList,
          resultField: 'items',
          selectProps: {
            disabled: true,
            fieldNames: {
              key: 'key',
              value: 'id',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      required: true
    },
    {
      field: 'account',
      label: '账号',
      component: 'Input',
      required: true
    },
    {
      field: 'account_name',
      label: '收款人名称',
      component: 'Input',
      required: true
    },
    {
      field: 'inCharge',
      label: '负责人',
      component: 'ApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: getInchargeList,
          resultField: 'items',
          selectProps: {
            fieldNames: {
              key: 'key',
              value: 'id',
              label: 'name'
            },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            disabled: formModel.is_dept_batch == 1 || formModel.sales_work_id ? true : false
          }
        }
      },
      itemProps: {
        validateTrigger: 'blur'
      },
      required({ model }) {
        return model.is_dept_batch == 1 ? false : true
      },
      dynamicDisabled({ model }) {
        return model.is_dept_batch == 1 ? true : false
      }
    },
    {
      field: 'bank',
      label: '开户行',
      required: true,
      component: 'Input'
    },
    {
      field: 'remark',
      label: '支出备注',
      component: 'InputTextArea'
    },
    {
      field: 'desc',
      label: '支出摘要',
      component: 'InputTextArea'
    },
    {
      field: 'files',
      label: '附件',
      component: 'Upload',
      slot: 'Files',
      helpMessage: '票据附件与明细附件至少上传一个'
    }
  ]
  return updateFormSchema
}
export const excelHeader = ['支出摘要', '支出金额', '支出科目名称', '支出科目代码', '支出部门名称']

export const childrenColumns = (salesList: any[], hand?: Function, type?: any, order?: any, check?: any): BasicColumn[] => {
  const newDetailsColumns: Array<any> = []
  const datas = cloneDeep(updataexpenseDetails(salesList, hand, type, order, check))
  for (const item of datas) {
    if (item.dataIndex == 'corres_pondent') {
      item.editComponent = 'Input'
      item.editComponentProps = {}
    }
    newDetailsColumns.push(item)
  }
  return newDetailsColumns
}

export const purchasedata = ref()
export const department = ref()
export const income = ref()
export const purchasePagingSelectConfig = {
  resultField: 'items',
  api: mfgetCashierAndOrder,
  searchMode: true,
  pagingMode: true,
  pagingSize: 20,
  searchParamField: 'order_number',
  selectProps: {
    fieldNames: {
      key: 'key',
      value: 'order_number',
      label: 'order_number'
    },
    showSearch: true,
    placeholder: '请选择',
    optionFilterProp: 'order_number',
    allowClear: true,
    onChange: async (_, shall) => {
      console.log(shall)
      purchasedata.value = shall
      department.value = shall?.department
      income.value = shall?.income
    },
    style: {
      width: '100%'
    }
  }
}
export const projectPagingSelectConfig = {
  resultField: 'items',
  api: getProjectList,
  searchMode: true,
  pagingMode: true,
  pagingSize: 20,
  searchParamField: 'id',
  selectProps: {
    fieldNames: {
      key: 'key',
      value: 'id',
      label: 'project_name'
    },
    showSearch: true,
    placeholder: '请选择',
    optionFilterProp: 'id',
    allowClear: true,
    style: {
      width: '100%'
    }
  }
}
