import { DescItem } from '/@/components/Description'
import { BasicColumn } from '/@/components/Table'

const INVOICE_TYPE = {
  1: { text: '增值税发票 ' },
  2: { text: '普通发票' },
  3: { text: '不开票' }
}
const PAYMENT_TYPE = {
  1: { text: '定金 ' },
  2: { text: '最后一笔款' },
  3: { text: '全款' }
}

export const columns: BasicColumn[] = [
  {
    title: '项目号',
    dataIndex: 'project_number',
    width: 120,
    resizable: true
  },
  {
    title: '订单号',
    dataIndex: 'source_uniqid',
    width: 120,
    resizable: true
  },
  {
    title: '采购订单号',
    dataIndex: 'strid',
    width: 120,
    resizable: true
  },
  {
    title: '发票类型',
    dataIndex: 'invoice_type',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return INVOICE_TYPE[text]?.text
    }
  },
  {
    title: '采购单总金额',
    dataIndex: 'total_price',
    width: 120,
    resizable: true
  },
  {
    title: '登记发票金额',
    dataIndex: 'i_amount',
    width: 120,
    resizable: true
  },
  {
    title: '已报关金额',
    dataIndex: 'cus_amount',
    width: 120,
    resizable: true
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    width: 120,
    resizable: true
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
    resizable: true
  }
]

export const descItems: DescItem[] = [
  {
    field: 'project_number',
    label: '项目号'
  },
  {
    field: 'source_uniqid',
    label: '销售单号'
  },
  {
    field: 'pur_strid',
    label: '采购单号'
  },
  {
    field: 'supplier_name',
    label: '供应商名称'
  },
  {
    field: 'invoice_type',
    label: '开发票类型',
    render: (curVal) => {
      return INVOICE_TYPE[curVal]?.text
    }
  },
  {
    field: 'total_price',
    label: '采购订单总金额(含税)'
  },
  {
    field: 'i_amount',
    label: '采购登记发票金额'
  },
  {
    field: 'cus_amount',
    label: '已报关发票金额'
  },
  {
    field: 'department',
    label: '部门'
  }
]

export const invcolumns: BasicColumn[] = [
  {
    title: '采购订单号',
    dataIndex: 'pur_strid',
    width: 120,
    resizable: true
  },
  {
    title: '发票金额',
    dataIndex: 'amount',
    width: 120,
    resizable: true
  },
  {
    title: '发票号码',
    dataIndex: 'number',
    width: 120,
    resizable: true
  },
  {
    title: '发票类型',
    dataIndex: 'invoice_type',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return INVOICE_TYPE[text]?.text
    }
  }
]
export const curcolumns: BasicColumn[] = [
  {
    title: '采购订单号',
    dataIndex: 'pur_strid',
    width: 120,
    resizable: true
  },

  {
    title: '发票号码',
    dataIndex: 'number',
    width: 120,
    resizable: true
  },
  {
    title: '品名',
    dataIndex: 'tax_service_name',
    width: 120,
    resizable: true
  },
  {
    title: '规格型号',
    dataIndex: 'size',
    width: 120,
    resizable: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 120,
    resizable: true
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    width: 120,
    resizable: true
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    width: 120,
    resizable: true
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 120,
    resizable: true
  },
  {
    title: '税率',
    dataIndex: 'tax_rate',
    width: 120,
    resizable: true
  },
  {
    title: '税额',
    dataIndex: 'tax_amount',
    width: 120,
    resizable: true
  },
  {
    title: '税价合计',
    dataIndex: 'tax_total_amount',
    width: 120,
    resizable: true
  }
]
export const rwcolumns: BasicColumn[] = [
  {
    title: '付款单号',
    dataIndex: 'strid',
    width: 120,
    resizable: true
  },

  {
    title: '付款日期',
    dataIndex: 'collection_at',
    width: 120,
    resizable: true
  },
  {
    title: '付款主体',
    dataIndex: 'contracting_party',
    width: 120,
    resizable: true
  },
  {
    title: '付款类型',
    dataIndex: 'payment_type',
    width: 120,
    resizable: true,
    customRender: ({ text }) => {
      return PAYMENT_TYPE[text]?.text
    }
  },
  {
    title: '付款金额',
    dataIndex: 'amount',
    width: 120,
    resizable: true
  }
]
