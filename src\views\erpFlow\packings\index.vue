<template>
  <div class="p-4">
    <BasicTable :data-cachekey="routePath" @register="registerTable">
      <template #toolbar>
        <Button v-if="hasPermission(716)" type="primary" @click="handledeliveryExport()">批量导出交付装箱单</Button>
        <Button v-if="hasPermission(693)" type="primary" @click="handlewarehouseExport()">批量导出仓库装箱单</Button>
        <Button v-if="hasPermission(521)" type="primary" @click="handleapprovedstatus([], 'batch')">批量出库特批通过</Button>
        <Button v-if="hasPermission(159)" type="primary" @click="handleBeforeExport" :loading="batchExportLoading">批量导出装箱单</Button>
        <Button
          v-if="hasPermission(504)"
          type="primary"
          @click="handleBeforeApprove"
          :disabled="batchApproveLoading"
          :loading="batchApproveLoading"
          >批量审批</Button
        >
        <!--        <Button type="primary" @click="handleImport">导入装箱单</Button>-->
        <Button v-if="hasPermission(485)" :loading="addLoading" type="primary" @click="handleAddPackings">创建出库单</Button>
        <!--        <input v-show="false" ref="inputFile" type="file" @input="handleImportChange" />-->
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <div class="mb-2">
          装箱单包裹：<Input
            v-model:value="record.searchValue"
            placeholder="请输入包裹号/销售订单/采购订单，可进行搜索"
            style="width: 400px"
            allow-clear
          />
          是否备货:
          <Select
            :options="[
              { label: '是', value: 1 },
              { label: '否', value: 0 }
            ]"
            v-model:value="record.selectisStock"
            allow-clear
            style="width: 400px"
          />
          <Button class="ml-2" type="primary" @click="handleSearchPackage(record)">搜索</Button>
          <Tooltip title="勾选后批量删除包裹">
            <Button class="ml-2" v-if="hasPermission(660)" type="primary" @click="handledelete(record, selectPackages)"
              >批量删除包裹</Button
            >
          </Tooltip>
        </div>
        <BasicTable
          class="expand-row-table"
          :canResize="false"
          :data-source="record.compPackages"
          :columns="childColumns"
          :maxHeight="400"
          :rowSelection="{
            onChange: (selectedRowKeys, selectedRows) => {
              selectPackages = selectedRows
            }
          }"
          :actionColumn="{
            title: '操作',
            dataIndex: 'action',
            width: 220,
            fixed: 'right'
          }"
        >
          <template #bodyCell="{ record: childRecord, column }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction :actions="createChildActions(record, childRecord)" />
            </template>
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <PkgOutWarehouseDrawer @register="registerPkgOutDrawer" @success="reload" />
    <warningModal @register="registerWarningModal" @packingedit="packingedit" @packingopen="packingopen" />
    <ApplyDrawer @register="registerApply" @success="reload" />
    <applyModal @register="registerApplyModal" @success="reload" />
    <UploadModal @register="registerUploadModal" @success="handleSuccess" />
  </div>
</template>

<script setup lang="tsx" name="/erp/packings">
import { ActionItem, BasicTable, TableAction, useTable } from '/@/components/Table'
import { columns, getPath, childColumns } from './datas/datas'
import { Button, message, Form, Textarea, Input, Select, Tooltip } from 'ant-design-vue'
// import { ref } from 'vue'
// import { WorkSheet } from 'xlsx'
import { useGo } from '/@/hooks/web/usePage'
import {
  exportPackage,
  setPackingStatus,
  getPackingListByDate,
  setApprovedStatus,
  setIsCancel,
  packingcancelStatus,
  updatePackage,
  removePackingPackage,
  packingexportWarehousePacking,
  packingexportDeliverPacking
} from '/@/api/erpFlow/packings'
import { searchFromSchemas } from './datas/datas'
import { IData } from './datas/types'
import { useMessage } from '/@/hooks/web/useMessage'
import { ref } from 'vue'
import { usePermission } from '/@/hooks/web/usePermission'
import PkgOutWarehouseDrawer from '/@/views/erp/outWarehouse/components/PkgOutWarehouseDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import warningModal from '../packages/components/warningModal.vue'
import { useModal } from '/@/components/Modal'
import { checkPackage, getPackageDetail, getPackageList, packagecancelStock } from '/@/api/erpFlow/packages'
import ApplyDrawer from './components/applyDrawer.vue'
import { cloneDeep, pick } from 'lodash-es'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'
import { PagingApiSelect } from '/@/components/Form'
import ExcelJS from 'exceljs'
import { createExcelWorksheet, addDataRows, exportExcelFile } from './datas/datas'
import applyModal from './components/applyModal.vue'
import UploadModal from './components/UploadModal.vue'

const FormItem = Form.Item
import { useRoute } from 'vue-router'

const route = useRoute()
const { path: routePath } = route
const pathname = window.location.pathname
const batchApproveLoading = ref<boolean>(false)
const { hasPermission } = usePermission()
const { createMessage, createConfirm } = useMessage()
const go = useGo()
//编辑,详情id
const packingeditID = ref()
const reject_remark = ref('')
const cancel_remark = ref('')
const addLoading = ref(false)
const selectPackages = ref()

const [registerWarningModal, { openModal, setModalProps }] = useModal()
const [registerApplyModal, { openModal: openApplyModal }] = useModal()
const [registerApply, { openDrawer: openApply }] = useDrawer()
const [registerUploadModal, { openModal: openModalUplad }] = useModal()

// const jsonData = ref<PackageOrder>(cloneDeep(commonPackingInfo))
// const inputFile = ref<HTMLInputElement | null>(null)
const [registerTable, { reload, getSelectRows, getSelectRowKeys, clearSelectedRowKeys, setLoading }] = useTable({
  title: '装箱单',
  showIndexColumn: false,
  columns,
  api: getPackingListByDate,
  useSearchForm: true,
  showTableSetting: true,
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFromSchemas,
    fieldMapToTime: [
      ['shipment_at', ['shipmentAtStart', 'shipmentAtEnd'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['created_at', ['createdAtStart', 'createdAtEnd'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]
    ]
  },
  rowKey: 'id',
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    width: 350,
    fixed: 'right'
  },
  rowSelection: {},
  afterFetch: (data) => {
    clearSelectedRowKeys()
    data.forEach((item) => {
      item.compPackages = item.packages
    })
    return data
  }
  // pagination: false,
  // beforeFetch: handleBeforeFetch,
  // afterFetch: handleAfterFetch
})

function createActions(record: Recordable): ActionItem[] {
  return [
    {
      icon: 'ant-design:file-search-outlined',
      label: '审核',
      popConfirm: {
        title: '审核后将无法撤回，且无法编辑，是否确定审核？',
        placement: 'left',
        confirm: handleApprove.bind(null, [record.id]),
        disabled: record.status !== 0 || record.is_cancel == 1
      },
      ifShow: hasPermission(157)
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: record.status !== 0 || record.is_cancel == 1,
      ifShow: hasPermission(155) && hasPermission(139)
    },
    {
      label: '追加包裹',
      icon: 'octicon:package-dependencies-24',
      ifShow: hasPermission(155) && hasPermission(139),
      disabled: record.status !== 0 || record.is_cancel == 1,
      popConfirm: {
        visible: record.isVisible,
        title: (
          <div>
            <PagingApiSelect
              v-model:value={record.pushPackages}
              placeholder="请选择包裹，输入销售单号可查出对应的未装箱包裹"
              mode="multiple"
              resultField="items"
              api={(params) =>
                getPackageList({
                  ...params,
                  is_old: 0,
                  is_packing: 0,
                  is_out: 0,
                  is_cancel: 0,
                  is_retreat: 0,
                  is_stock: 0,
                  is_scrap: 0,
                  project_number: record.project_number
                })
              }
              searchMode={true}
              pagingMode={true}
              pagingSize={20}
              searchParamField="source_uniqid"
              selectProps={{
                class: 'w-[300px]',
                allowClear: true,
                fieldNames: { value: 'id', label: 'strid' },
                showSearch: true,
                placeholder: '请选择'
              }}
            />
          </div>
        ),
        disabled: record.status !== 0 || record.is_cancel == 1,
        placement: 'left',
        confirm: handleAddPackage.bind(null, record)
      }
    }
  ]
}

function handleSearchPackage(record) {
  if (!record.searchValue && record.selectisStock === undefined) {
    record.compPackages = record.packages
    return
  }

  const searchValue = record?.searchValue?.toLowerCase()
  record.compPackages = record.packages.filter((item) => {
    // 文本搜索匹配
    const textMatch = !searchValue
      ? true
      : item.strid?.toLowerCase().includes(searchValue) ||
        item.source_uniqids?.some((sourceId) => sourceId?.source_uniqid.toString().toLowerCase().includes(searchValue)) ||
        item.purchase_strids?.some((purchaseId) => purchaseId?.strid.toString().toLowerCase().includes(searchValue))

    // 备货状态匹配
    const stockMatch = record.selectisStock === undefined ? true : item.is_stock === record.selectisStock

    return textMatch && stockMatch
  })
}

function createChildActions(record: Recordable, childRecord: Recordable): ActionItem[] {
  return [
    {
      label: '移出该包裹',
      tooltip: '移出后，可在其他同项目装箱单中追加此包裹',
      popConfirm: {
        title: '是否确定移出该包裹？',
        placement: 'left',
        confirm: removePackage.bind(null, record, childRecord),
        disabled: record.status !== 0 || record.is_cancel == 1,
        visible: record.isVisible
      },
      disabled: record.status !== 0 || record.is_cancel == 1,
      ifShow: hasPermission(155) && hasPermission(139)
    },
    {
      label: '取消备货',
      tooltip: '取消后，可在包裹中重新设置备货',
      popConfirm: {
        title: '是否确定取消该包裹备货状态？',
        placement: 'left',
        confirm: hanlecancelStock.bind(null, record, childRecord),
        disabled: childRecord.is_stock !== 1,
        visible: record.isVisible
      },
      disabled: childRecord.is_stock !== 1,
      ifShow: hasPermission(630)
    }
  ]
}

async function removePackage(record, childRecord) {
  try {
    if (childRecord.disabled) return createMessage.error('正在执行，请稍等！')
    childRecord.disabled = true
    if (record.packages.length <= 1) return createMessage.error('请至少保留一个包裹！')
    setLoading(true)
    const { msg } = await removePackingPackage({ id: record.id, packing_package_ids: [childRecord.id] })
    record.isVisible = false
    if (msg === 'success') {
      return createMessage.success('移出成功')
    }
    createMessage.error('移出失败')
  } catch (err) {
    createMessage.error('移出失败')
  } finally {
    reload()
    setLoading(false)
  }
}

async function handleAddPackage(record) {
  try {
    if (record.addPackage) return createMessage.error('正在执行，请稍等！')
    record.addPackage = true
    if (record.pushPackages.length === 0) return createMessage.error('请选择需要添加的包裹！')
    setLoading(true)
    const params = {
      ...pick(record, ['id', 'buyer', 'country', 'supplier', 'cabinet_number', 'plate_number', 'remark', 'urgent_level']),
      shipmentAt: record.shipment_at,
      shipmentAddr: record.shipment_addr,
      packingList: record.pushPackages.map((id) => ({ id }))
    }
    const { msg } = await updatePackage(params)
    record.isVisible = false
    if (msg === 'success') {
      return createMessage.success('追加包裹成功')
    }
    createMessage.error('追加包裹失败')
  } catch (err) {
    createMessage.error('追加包裹失败')
  } finally {
    reload()
    setLoading(false)
  }
}

function createDropDownActions(record): ActionItem[] {
  return [
    {
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission(139) && hasPermission(156)
    },
    {
      label: '导出',
      onClick: handleExport.bind(null, [record.id]),
      ifShow: hasPermission([158]),
      disabled: record.is_cancel == 1
    },
    {
      label: '取消审核',
      popConfirm: {
        title: '确认取装箱单的审核状态？',
        placement: 'left',
        confirm: handleUnApprove.bind(null, record),
        disabled: record.status == 0 || record.is_cancel == 1
      },
      ifShow: hasPermission(595)
    },
    {
      label: '出库特批申请列表',
      onClick: handleappltlist.bind(null, record),
      ifShow: hasPermission([516]),
      disabled: record.is_cancel == 1
    },
    {
      icon: 'ant-design:file-search-outlined',
      label: '出库特批申请',
      onClick: handleApply.bind(null, [record.id]),
      disabled: [1, 2].includes(record.approved_status) || record.is_cancel == 1,
      ifShow: hasPermission(517)
    },
    {
      icon: 'ant-design:file-search-outlined',
      label: '出库特批驳回',
      popConfirm: {
        title: (
          <div style={{ width: '400px' }}>
            <Form>
              <FormItem label={'驳回备注'} required>
                <Textarea v-model:value={reject_remark.value} />
              </FormItem>
            </Form>
          </div>
        ),
        placement: 'left',
        confirm: handleapprovedstatus.bind(null, [record.id], 'oneself'),
        disabled: [0, 2, 3].includes(record.approved_status) || record.is_cancel == 1
      },
      ifShow: hasPermission(518)
    },
    {
      icon: 'ant-design:file-search-outlined',
      label: '出库特批取消',
      popConfirm: {
        title: '确定取消出库特批吗？',
        placement: 'left',
        confirm: handleapprovedstatus.bind(null, [record.id], 'cancel'),
        disabled: [0, 2, 3].includes(record.approved_status) || record.is_cancel == 1
      },
      ifShow: hasPermission(519)
    },
    {
      icon: 'ant-design:file-search-outlined',
      label: '装箱单作废',
      popConfirm: {
        title: (
          <div style={{ width: '400px' }}>
            <Form>
              <FormItem label={'作废备注'} required>
                <Textarea v-model:value={cancel_remark.value} />
              </FormItem>
            </Form>
          </div>
        ),
        placement: 'left',
        confirm: handlesetIsCancel.bind(null, [record.id]),
        disabled: record.status !== 0 || record.is_cancel == 1
      },
      ifShow: hasPermission(520)
    },
    {
      icon: 'ant-design:upload-outlined',
      color: 'error',
      label: '箱单附件',
      onClick: handleUppload.bind(null, record),
      ifShow: hasPermission([739])
    }
  ]
}

function handleBeforeApprove() {
  const row = getSelectRows()
  const valid = row.every((item) => item.status == 0 && item.is_cancel != 1)
  if (!valid) return createMessage.error('存在未审核或已经取消的订单！')
  createConfirm({
    iconType: 'warning',
    title: '批量审核',
    content: '正在进行批量审核，确定将选中数据通过审核吗？',
    onOk: () => {
      const ids = getSelectRows().map((item) => item.id)
      handleApprove(ids)
    }
  })
}

const [registerPkgOutDrawer, { openDrawer: openPkgOutDrawer, setDrawerProps: setPkgOutDrawerProps }] = useDrawer()

const batchExportLoading = ref<boolean>(false)

// function handleEventBus() {
//   delay(() => {
//     reload()
//   }, 400)
// }

// onMounted(() => {
//   pageEventBus.on('package-table-reload', handleEventBus)
// })

// async function handleImport() {
//   // 打开文件选择器
//   inputFile.value?.click()
// }

// 装箱单导入
// async function handleImportChange(e: ChangeEvent) {
//   if (!(e.target as HTMLInputElement).files!.length) return
//   const file = (e.target as HTMLInputElement).files![0]
//   const data = await file.arrayBuffer()
//
//   const wb = XLSX.read(data)
//   const ws: WorkSheet = wb.Sheets[wb.SheetNames[0]]
//
//   // 处理单元格信息
//   const fileData = generateExcelInfo(ws)
//   rawCellData.value = fileData
//   // 单元格数据转成json对象
//   jsonData.value = genderPackingInfo(fileData)
// }

// function handleAdd() {
//   go({ path: getPath('add') })
// }

async function handleEdit(record: IData) {
  console.log(record)
  // const ids = record.packages.map((item) => item.id)
  const packageDetails = await getPackageDetail({ packing_ids: [record.id] })
  const ids = packageDetails.items.map((item) => item.id)
  if (!['/sp/', '/sptest/'].includes(pathname)) {
    const { items } = await checkPackage({ ids: ids })
    const totalarrears = items.saleList.reduce((total, item) => total + item.arrears, 0)
    if (totalarrears < -1 || items.noPackingCount.length > 0) {
      openModal(true, { record: items, type: 'packingedit' })
      setModalProps({ title: '警告' })
      packingeditID.value = record.id
    } else {
      // console.log(record)
      go({ path: getPath('edit'), query: { id: record.id } })
    }
  } else {
    go({ path: getPath('edit'), query: { id: record.id } })
  }
}

async function handleDetail(record: IData) {
  // const ids = record.packages.map((item) => item.id)
  const packageDetails = await getPackageDetail({ packing_ids: [record.id] })
  const ids = packageDetails.items.map((item) => item.id)
  if (!['/sp/', '/sptest/'].includes(pathname)) {
    const { items } = await checkPackage({ ids: ids })
    const totalarrears = items.saleList.reduce((total, item) => total + item.arrears, 0)
    if (totalarrears < -1 || items.noPackingCount.length > 0) {
      openModal(true, { record: items, type: 'packingdetail' })
      setModalProps({ title: '警告' })
      packingeditID.value = record.id
    } else {
      go({ path: getPath('detail'), query: { id: record.id } })
    }
  } else {
    go({ path: getPath('detail'), query: { id: record.id } })
  }
}

function packingedit(e) {
  go({ path: getPath(e), query: { id: packingeditID.value } })
}

//tab方法
// function handleBeforeFetch(params) {
//   const fieldsArr = ['shipmentAtStart', 'shipmentAtEnd', 'createdAtStart', 'createdAtEnd']
//   for (const key of Object.keys(params)) {
//     if (fieldsArr.includes(key) && isNaN(Date.parse(params[key]))) {
//       params[key] = void 0
//     }
//   }
//   return params
// }

// function handleAfterFetch(result) {
//   const data: IData[] = []
//   for (const key in result) {
//     data.push({
//       date: key,
//       return_order_no: result[key].map((item) => ({ ...item, isSelect: false }))
//     })
//   }
//
//   // data.forEach((item) => {
//   //   item.return_order_no.forEach((val) => {
//   //     Reflect.set(val, 'commodityList', [])
//   //     val.packages.forEach((event) => {
//   //       event.items.forEach((itemlist) => {
//   //         val.commodityList.push(itemlist)
//   //       })
//   //     })
//   //   })
//   // })
//   console.log(result, 'data')
//   return data.sort((a, b) => +new Date(a.date) - +new Date(b.date))
// }

// async function handleDel(record) {
//   try {
//     const { msg } = await delPacking(record.id)
//     if (msg === 'success') {
//       createMessage.success('删除成功')
//       await reload()
//     }
//   } catch (e) {
//     createMessage.success('删除失败')
//     throw new Error(`${e}`)
//   }
// }

async function handleApprove(ids) {
  try {
    batchApproveLoading.value = true
    const { msg } = await setPackingStatus({ ids })
    if (msg === 'success') {
      createMessage.success('审核成功')
      await reload()
    }
  } catch (e) {
    createMessage.error('审核失败')
    throw new Error(`${e}`)
  } finally {
    batchApproveLoading.value = false
  }
}

async function handleBeforeExport() {
  try {
    batchExportLoading.value = true
    setLoading(true)
    // const itemList = getDataSource().reduce((acc, cur) => {
    //   return [...acc, ...cur.return_order_no]
    // }, [])
    const itemList = getSelectRows()
    // if (itemList.length === 0 || itemList.some((record) => record.status !== 1)) {
    //   batchExportLoading.value = false
    //   return createMessage.error('请勾选已装箱的装箱单进行导出！')
    // }
    const ids = itemList.map((item) => item.id)
    if (ids.length === 0) {
      return createMessage.error('请先勾选需要导出的装箱单！')
    }
    await handleExport(ids)
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    setLoading(false)
    batchExportLoading.value = false
  }
}

async function handleExport(ids: number[]) {
  // 这个函数需要调用2次，第一次是为了验证后端是否返回二进制文件，如果返回json表示发生了错误
  // 第二次才是导出
  // 正常做法应该是第一次调用验证是否可以导出的新接口，而不是这个接口调用2次
  try {
    try {
      await exportPackage(true, ids)
    } catch (err) {
      console.log(err)
      if (err.message === 'Reflect.has called on non-object') {
        const response = await exportPackage(false, ids)
        // const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const downloadLink = document.createElement('a')
        downloadLink.href = URL.createObjectURL(response)
        downloadLink.download = `敬城建材国际商城统一装箱单-${+new Date()}.xlsx`

        // 模拟点击下载链接
        downloadLink.click()

        // 清理临时资源
        URL.revokeObjectURL(downloadLink.href)
        createMessage.success('导出成功')
      }
      throw new Error(`${e}`)
    }
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    batchExportLoading.value = false
  }
}

// function handleSelected(cardData) {
//   if (cardData.status === 1) Reflect.set(cardData, 'isSelect', !cardData.isSelect)
// }

// function handleSelectRow(record, isSelect: boolean) {
//   const tableData = getDataSource()
//   const curData = tableData.find((item) => item.date === record.date)
//   if (!curData) return createMessage.error('没有找到对应数据！')
//   const newData = {
//     ...curData,
//     return_order_no: curData.return_order_no.map((item) => ({ ...item, isSelect: item.status === 1 ? isSelect : false }))
//   }
//   updateTableDataRecord(record.date, newData)
// }

// function handleSelectAllRow(isSelect: boolean) {
//   const tableData = getDataSource()
//   const newData = tableData.map((record) => ({
//     ...record,
//     return_order_no: record.return_order_no.map((item) => ({ ...item, isSelect: item.status === 1 ? isSelect : false }))
//   }))
//   setTableData(newData)
// }

async function handleAddPackings() {
  try {
    addLoading.value = true
    const tableData = cloneDeep(getSelectRows())
    if (tableData.length === 0) return createMessage.error('请先点击选中需要创建出库单的装箱单')
    const isApprove = tableData.every((item) => item.status === 1 && item.is_cancel !== 1)
    if (!isApprove) return createMessage.error('存在没有通过审批及作废及作废的装箱单！')
    let isout
    for (const item of tableData) {
      isout = item.packages[0].is_out == 0
    }
    //判断是否特批
    const approvedstatus = tableData.every((item) => item.approved_status === 2)
    if (!isout) return createMessage.error('已生成出库单，请勿重复做单。（出库中的清审核出库单）！')
    // const ids = ref<any>([])
    // for (const item of tableData) {
    //   const id = item.packages.map((val) => val.id)
    //   ids.value = [...ids.value, ...id]
    // }

    const packingIds = tableData.map((item) => item.id)
    const packageDetail = await getPackageDetail({ packing_ids: packingIds })

    const ids = packageDetail.items.map((item) => item.id)
    if (!['/sp/', '/sptest/'].includes(pathname)) {
      const { items } = await checkPackage({ ids: ids })
      const totalarrears = items.saleList.reduce((total, item) => total + item.arrears, 0)
      if (totalarrears < -1 && !approvedstatus) {
        openModal(true, { record: items, type: 'packing' })
        setModalProps({ title: '警告' })
      } else if (items.saleList.length == 0 && items.noPackingCount.length > 0) {
        openModal(true, { record: items, type: 'noPackingCount', table: tableData })
        setModalProps({ title: '警告' })
      } else {
        openPkgOutDrawer(true, { type: 'add', packings: tableData })
        setPkgOutDrawerProps({ title: '新增出库单', showFooter: true })
      }
    } else {
      openPkgOutDrawer(true, { type: 'add', packings: tableData })
      setPkgOutDrawerProps({ title: '新增出库单', showFooter: true })
    }
  } catch (err) {
    createMessage.error('创建出库单失败')
  } finally {
    addLoading.value = false
  }
}

function packingopen(table) {
  openPkgOutDrawer(true, { type: 'add', packings: table })
  setPkgOutDrawerProps({ title: '新增出库单', showFooter: true })
}

//申请
function handleApply(e) {
  openApplyModal(true, { id: e[0] })
}

function handleapprovedstatus(record, type) {
  switch (type) {
    case 'batch':
      const tableDatas = getSelectRows()
      const res = tableDatas.every((item) => item.approved_status === 1 && item.is_cancel !== 1)
      if (res) {
        const tabledata = getSelectRowKeys()
        const ids = tabledata.map((item) => {
          return {
            id: item,
            approved_status: 2
          }
        })
        console.log(ids)
        setApprovedStatus({ ids: ids })
        reload()
      } else {
        message.error('请选择未审批及未作废的装箱单')
      }
      break
    case 'oneself':
      setApprovedStatus({ ids: [{ id: record[0], approved_status: 3, reject_remark: reject_remark.value }] })
      reload()
      reject_remark.value = ''
      break
    case 'cancel':
      setApprovedStatus({ ids: [{ id: record[0], approved_status: 0 }] })
      reload()
      break
  }
}

function handlesetIsCancel(record) {
  setIsCancel({ id: record[0], is_cancel: 1, cancel_remark: cancel_remark.value })
  reload()
}

function handleappltlist(record) {
  openApply(true, { id: record.id, approved_status: record.approved_status })
}

async function handleUnApprove(record) {
  try {
    await packingcancelStatus({ id: record.id })
    await reload()
  } catch (e) {
    console.log(e)
  }
}

async function hanlecancelStock(record, recordfilter) {
  console.log(record, recordfilter)

  try {
    if (recordfilter.disabled) return createMessage.error('正在执行，请稍等！')
    recordfilter.disabled = true
    setLoading(true)
    const stockList = [
      {
        id: recordfilter.id,
        packing_id: record.id
      }
    ]
    const { msg } = await packagecancelStock({ stockList, stock_source: 1 })
    record.isVisible = false
    if (msg === 'success') {
      return createMessage.success('取消备货成功')
    }
    createMessage.error('取消备货失败')
  } catch (err) {
    createMessage.error('取消备货失败')
  } finally {
    reload()
    setLoading(false)
  }
}

async function handledelete(record, selectPackages) {
  console.log(record)
  try {
    if (!selectPackages) return createMessage.error('请选择要删除的包裹')
    if (record.packages.length === selectPackages.length) return createMessage.error('请至少保留一个包裹,如要删除包裹，请作废该装箱单！')
    setLoading(true)
    removePackingPackage({ id: record.id, packing_package_ids: selectPackages.map((item) => item.id) })
  } catch (e) {
    console.log(e)
    createMessage.error('删除失败')
  } finally {
    await reload()
    setLoading(false)
  }
}

async function handlewarehouseExport() {
  // 创建一个消息实例，用于显示导出进度
  const messageKey = 'export-progress'
  try {
    setLoading(true)

    // 使用getSelectRowKeys获取勾选的ID
    const selectedIds = getSelectRowKeys()
    if (!selectedIds || selectedIds.length === 0) return createMessage.error('请选择要导出的装箱单')
    message.loading({ content: '准备导出数据...', key: messageKey, duration: 0 })
    // 使用packingexportWarehousePacking接口获取数据
    const response = await packingexportWarehousePacking({ ids: selectedIds })

    if (!response || !response.items || response.items.length === 0) {
      createMessage.error('未找到装箱单数据')
      return
    }

    // 创建新的工作簿
    const workbook = new ExcelJS.Workbook()

    // 遍历每个装箱单
    for (let i = 0; i < response.items.length; i++) {
      const item = response.items[i]
      // 更新进度消息
      const progress = Math.floor(((i + 1) / response.items.length) * 100)
      message.loading({
        content: `正在处理装箱单数据 (${i + 1}/${response.items.length})... ${progress}%`,
        key: messageKey,
        duration: 0
      })

      // 创建工作表
      const worksheet = await createExcelWorksheet(workbook, item)
      // 添加数据行
      await addDataRows(worksheet, workbook, item)
    }

    // 更新进度消息
    message.loading({ content: '正在生成Excel文件...', key: messageKey, duration: 0 })

    // 导出Excel文件
    await exportExcelFile(workbook)
    message.success({ content: '导出成功', key: messageKey })
  } catch (error) {
    console.error(error)
    message.error({ content: '导出失败', key: messageKey })
  } finally {
    setLoading(false)
  }
}

async function handledeliveryExport() {
  try {
    batchExportLoading.value = true
    setLoading(true)
    // const itemList = getDataSource().reduce((acc, cur) => {
    //   return [...acc, ...cur.return_order_no]
    // }, [])
    const itemList = getSelectRows()
    // if (itemList.length === 0 || itemList.some((record) => record.status !== 1)) {
    //   batchExportLoading.value = false
    //   return createMessage.error('请勾选已装箱的装箱单进行导出！')
    // }
    const ids = itemList.map((item) => item.id)
    if (ids.length === 0) {
      return createMessage.error('请先勾选需要导出的装箱单！')
    }
    await handledeliverExport(ids)
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    setLoading(false)
    batchExportLoading.value = false
  }
}
async function handledeliverExport(ids: number[]) {
  // 这个函数需要调用2次，第一次是为了验证后端是否返回二进制文件，如果返回json表示发生了错误
  // 第二次才是导出
  // 正常做法应该是第一次调用验证是否可以导出的新接口，而不是这个接口调用2次
  try {
    try {
      await packingexportDeliverPacking(true, ids)
    } catch (err) {
      console.log(err)
      if (err.message === 'Reflect.has called on non-object') {
        const response = await packingexportDeliverPacking(false, ids)
        // const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const downloadLink = document.createElement('a')
        downloadLink.href = URL.createObjectURL(response)
        downloadLink.download = `敬城建材国际商城统一装箱单-${+new Date()}.xlsx`

        // 模拟点击下载链接
        downloadLink.click()

        // 清理临时资源
        URL.revokeObjectURL(downloadLink.href)
        createMessage.success('导出成功')
      }
      throw new Error(`${e}`)
    }
  } catch (e) {
    throw new Error(`${e}`)
  } finally {
    batchExportLoading.value = false
  }
}

// 上传附件
function handleUppload(record) {
  openModalUplad(true, record)
}
</script>
<style scoped lang="less">
.expand-row-table {
  :deep(.ant-table) {
    margin: 0 !important;
  }
}
</style>
<style>
.ant-select-dropdown {
  z-index: 9999 !important;
}
</style>
