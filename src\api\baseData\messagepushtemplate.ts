//资金档案
import { defHttp } from '/@/utils/http/axios'
enum Api {
  messageconfiggetList = '/erp/message/config/getList',
  messageconfigupdate = '/erp/message/config/update',
  messageconfigsetIsDisabled = '/erp/message/config/setIsDisabled'
}

export const messageconfiggetList = (params?: {}) => defHttp.get({ url: Api.messageconfiggetList, params })
export const messageconfigupdate = (params?: {}) =>
  defHttp.post({ url: Api.messageconfigupdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const messageconfigsetIsDisabled = (params?: {}) =>
  defHttp.post({ url: Api.messageconfigsetIsDisabled, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
