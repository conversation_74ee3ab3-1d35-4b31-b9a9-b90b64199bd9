<template>
  <BasicModal @register="register" @ok="handleOk" :min-height="350">
    <BasicForm @register="registerform" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '../datas/drawer'
import { ref } from 'vue'
import { setinterStatus, setinterdelete } from '/@/api/credential/interior'

const type = ref('')
//id
const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
  type.value = data
  resetFields()
})
const [registerform, { validate, resetFields }] = useForm({
  schemas,
  showActionButtonGroup: false,
  baseColProps: { span: 12 },
  labelCol: { span: 5 }
})

const emit = defineEmits(['success', 'register'])
// 提交
async function handleOk() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    const res = type.value == 'status' ? await setinterStatus(formdata) : await setinterdelete(formdata)
    console.log(res)
    emit('success')
    closeModal()
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    throw new Error(`${e}`)
  }
}
</script>
