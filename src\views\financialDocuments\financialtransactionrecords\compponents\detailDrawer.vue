<template>
  <BasicDrawer @register="register" title="详情" width="90%">
    <Description :column="3" :schema="descItems" :data="datas" />
    <Descriptions title="付款任务" />
    <DescriptionsItem>
      <BasicTable @register="registerrwTable" />
    </DescriptionsItem>
    <Descriptions title="采购登记发票明细" />
    <DescriptionsItem>
      <BasicTable @register="registerinvTable" />
    </DescriptionsItem>
    <Descriptions title="报关发票明细" />
    <DescriptionsItem>
      <BasicTable @register="registercurTable" />
    </DescriptionsItem>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicTable, useTable } from '/@/components/Table'
import { Description } from '/@/components/Description/index'
import { curcolumns, descItems, invcolumns, rwcolumns } from '../datas/datas'
import { Invoicerwbdetails } from '/@/api/financialDocuments/InvoiceManagement'
import { ref } from 'vue'
import { Descriptions, DescriptionsItem } from 'ant-design-vue'

const datas = ref()

const [register] = useDrawerInner(async (data) => {
  try {
    const { items } = await Invoicerwbdetails({ pur_id: data.record.pur_id })
    datas.value = items.doc
    await setrwTableData(items.rw)
    await setinvTableData(items.inv)
    await setcurTableData(items.cur)
  } catch (e) {
    console.log(e)
  }
})

const [registerrwTable, { setTableData: setrwTableData }] = useTable({
  columns: rwcolumns,
  showIndexColumn: false,
  canResize: false
})
const [registerinvTable, { setTableData: setinvTableData }] = useTable({
  columns: invcolumns,
  showIndexColumn: false,
  canResize: false
})
const [registercurTable, { setTableData: setcurTableData }] = useTable({
  columns: curcolumns,
  showIndexColumn: false,
  canResize: false
})
</script>
