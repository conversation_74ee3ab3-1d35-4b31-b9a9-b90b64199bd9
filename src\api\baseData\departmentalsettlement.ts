//客户信息
import { defHttp } from '/@/utils/http/axios'

enum Api {
  dagetList = '/erp/da/getList',
  dacreateOrUpdate = '/erp/da/createOrUpdate',
  dadelete = '/erp/da/delete'
}

export const dagetList = (params: {}) => defHttp.get({ url: Api.dagetList, params })
export const dacreateOrUpdate = (params: {}) =>
  defHttp.post({ url: Api.dacreateOrUpdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const dadelete = (params: {}) =>
  defHttp.get({ url: Api.dadelete, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
