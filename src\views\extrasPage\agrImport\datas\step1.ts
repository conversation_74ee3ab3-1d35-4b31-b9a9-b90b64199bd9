// import { Rule } from 'ant-design-vue/lib/form'
import { lineBreakOptions, splitterOptions } from '../../importPurchase/datas/step1'
import { TRouteNameType } from './datas'
import { getWarehouse } from '/@/api/baseData/warehouse'
import { getClientList, getErpSupplier } from '/@/api/commonUtils'

import { FormSchema } from '/@/components/Form'
import { isNullOrUnDef } from '/@/utils/is'
import { message } from 'ant-design-vue'

export const inAndOutWarehouse: TRouteNameType[] = ['inwarehouse', 'outwarehouse', 'packageCheck']
export const purchaseOrInWarehouseRetreat: TRouteNameType[] = ['purchaseRetreat', 'inWarehouseRetreat']
const quantityOption = {
  value: 'quantity',
  label: '数量'
}

const inwarehouseparamsOptions = [
  {
    value: 'article',
    label: 'article'
  },
  {
    value: 'un',
    label: 'un'
  },
  quantityOption
]
const outwarehouseparamsOptions = [
  {
    value: 'article',
    label: 'article'
  },
  quantityOption
]
const saleOrderRetreatOptions = [
  {
    value: 'un',
    label: 'un'
  },
  quantityOption
]
const PurchaseOrInWarehouseRetreatOptions = [
  {
    value: 'id',
    label: 'id'
  },
  quantityOption
]

const packageCheckOptions = [
  {
    value: 'box_number',
    label: '箱号'
  },
  {
    value: 'article',
    label: 'article'
  },
  {
    value: 'quantity',
    label: '数量'
  },
  {
    value: 'net_weight',
    label: '净重'
  },
  {
    value: 'weight',
    label: '毛重'
  },
  {
    value: 'invoice_number',
    label: '发票号码+序号'
  },
  {
    value: 'tax_code',
    label: '税收分类编码'
  },
  {
    value: 'tax_name',
    label: '货物或应税劳务名称'
  },
  {
    value: 'specification',
    label: '规格型号'
  },
  {
    value: 'unit',
    label: '单位'
  },
  {
    value: 'box_length',
    label: '长度'
  },
  {
    value: 'box_width',
    label: '宽度'
  },
  {
    value: 'box_height',
    label: '高度'
  },
  {
    value: 'name_cn',
    label: '中文名称'
  },
  {
    value: 'sub_box_number',
    label: '子箱号'
  }
]

const mapOptionsFromType = {
  inwarehouse: inwarehouseparamsOptions,
  outwarehouse: outwarehouseparamsOptions,
  saleOrderRetreat: saleOrderRetreatOptions,
  purchaseRetreat: PurchaseOrInWarehouseRetreatOptions,
  inWarehouseRetreat: PurchaseOrInWarehouseRetreatOptions,
  packageCheck: packageCheckOptions
}

export const schemasFn: (newRouteNameType: TRouteNameType) => FormSchema[] = (newRouteNameType) => [
  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    required: true,
    ifShow: inAndOutWarehouse.slice(0, 1).includes(newRouteNameType),
    componentProps: () => {
      return {
        api: getErpSupplier,
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        resultField: 'items'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'warehouse_id',
    component: 'PagingApiSelect',
    label: '仓库',
    required: true,
    ifShow: inAndOutWarehouse.includes(newRouteNameType),
    componentProps: {
      pagingMode: true,
      searchMode: true,
      api: (params) => getWarehouse({ ...params }),
      resultField: 'items',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: false,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'invoice_number',
    component: 'Input',
    required: true,
    label: '发票号',
    ifShow: ['outwarehouse'].includes(newRouteNameType)
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'ApiSelect',
    required: true,
    ifShow: ['outwarehouse', 'packageCheck'].includes(newRouteNameType),
    componentProps: ({}) => ({
      api: getClientList,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      },

      resultField: 'items'
    }),
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'splitter',
    label: '值与值之间的分隔符',
    defaultValue: '\t',
    // defaultValue: ' ',
    component: 'Select',
    required: true,
    componentProps: { options: splitterOptions },
    rules: [
      {
        required: true,
        message: '请选择值与值之间的分隔符'
      },
      {
        validator(_, value) {
          return new Promise((resolve, reject) => {
            if (isNullOrUnDef(value)) return reject('请选择分隔符')

            resolve()
          })
        },
        trigger: 'blur'
      }
    ]
  },
  {
    field: 'lineBreak',
    label: '选择换行符',
    dynamicDisabled: true,
    defaultValue: '\n',
    component: 'Select',
    required: true,
    componentProps: { options: lineBreakOptions },
    rules: [
      {
        required: true,
        message: '请选择换行符'
      },
      {
        validator(_, value) {
          return new Promise((resolve, reject) => {
            if (isNullOrUnDef(value)) return reject('请选择换行符')

            resolve()
          })
        },
        trigger: 'blur'
      }
    ]
  },
  {
    field: 'params_name',
    component: 'Select',
    label: '参数名',
    dynamicDisabled: true,
    defaultValue: mapOptionsFromType[newRouteNameType].map((item) => item.value),
    componentProps: {
      options: mapOptionsFromType[newRouteNameType],
      mode: 'multiple'
    },

    colProps: {
      span: 24
    }
  },
  {
    field: 'params_value',
    label: '参数值',
    component: 'InputTextArea',
    helpMessage: '输入内容后请选择对应的分隔符！',
    required: true,
    colProps: {
      span: 24
    },
    componentProps: {
      rows: 9
    }
  }
]

export const segment = (formData, newRouteNameType: TRouteNameType) => {
  const { splitter, lineBreak, params_value, params_name } = formData

  if (!params_name || params_name.length === 0) {
    message.error('请选择参数名选项')
    return []
  }

  // 按行分割
  const rows = params_value.split(lineBreak).filter((row) => row.trim() !== '')

  const arr = rows.map((row) => {
    const cells = row.split(splitter)

    // 如果分割后的单元格数量不匹配参数名数量
    if (cells.length !== params_name.length) {
      message.error('请检查是否有多余空格或属性名数量不对')
      throw new Error('分割数据出错')
    }

    const obj = {}
    params_name.forEach((key, index) => {
      obj[key] = cells[index]?.trim() || '/'
    })
    return obj
  })

  return arr.map((item, index) => {
    item.quantity = Number(item.quantity)
    item.key = Date.now() + index

    if (['packageCheck'].includes(newRouteNameType)) {
      for (const key in item) {
        if (!['box_number', 'article', 'name_cn', 'sub_box_number'].includes(key)) {
          item[key] = Number(item[key])
        }
      }
    }
    return item
  })
}
