<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <detailDrawer @register="registerDrawer" />
  </div>
</template>
<script setup lang="ts">
import { columns } from './datas/datas'
import { InvoicerwbgetList } from '/@/api/financialDocuments/InvoiceManagement'
import { BasicTable, useTable, TableAction, ActionItem, EditRecordRow } from '/@/components/Table'
import detailDrawer from './compponents/detailDrawer.vue'
import { useDrawer } from '/@/components/Drawer'

const [registerDrawer, { openDrawer }] = useDrawer()
const [registerTable] = useTable({
  showTableSetting: true,
  showIndexColumn: false,
  columns,
  api: InvoicerwbgetList,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    width: '150px'
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  let editButtonList: ActionItem[] = [
    {
      label: '详情',
      onClick: handleDetail.bind(null, record)
    }
  ]
  return editButtonList
}

function handleDetail(record) {
  openDrawer(true, { record })
}
</script>
