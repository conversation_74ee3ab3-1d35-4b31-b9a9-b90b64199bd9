import { mapAudit } from '../../saleOrder/datas/datas'
import type { BasicColumn, FormSchema } from '/@/components/Table'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { isNull, isUnDef } from '/@/utils/is'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { getDeptTree } from '/@/api/admin/dept'
import { getStaffList } from '/@/api/baseData/staff'
import { mapValue, urgentLevel } from '/@/views/financialDocuments/common'

import { mapStatus } from '/@/views/financialDocuments/receiptOrder/datas/fn'
import dayjs from 'dayjs'
import { getDept } from '/@/api/erp/systemInfo'
import { DIVIDER_SCHEMA, GET_STATUS_SCHEMA } from '/@/const/status'

//订单类型和状态
const saleStore = useSaleOrderStore()

export const columns: BasicColumn[] = [
  {
    title: '开单时间',
    dataIndex: 'submited_at',
    width: 120,
    resizable: true,
    customRender: ({ value }) => {
      return isUnDef(value) || isNull(value) ? '' : dayjs(value).format('YYYY-MM-DD')
    }
  },
  {
    title: '销售订单号',
    dataIndex: 'source_uniqid',
    width: 200,
    resizable: true
  },
  {
    title: '项目id',
    dataIndex: 'project_number',
    width: 200,
    resizable: true
  },
  {
    title: '项目名称',
    dataIndex: 'project_name',
    width: 200,
    resizable: true
  },
  {
    title: '客户名称',
    dataIndex: 'client_name',
    width: 200,
    resizable: true
  },
  {
    title: '收款次数',
    dataIndex: 'receipt_num',
    width: 100,
    resizable: true
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }) => {
      return useRender.renderTag(saleStore.saleStatus[record.status], saleStore.statusColor[record.status])
    }
  },
  {
    title: '结算状态',
    dataIndex: 'is_audit',
    width: 150,
    resizable: true,
    customRender: ({ value }) => {
      if (isNull(value) || isUnDef(value)) return ''
      return useRender.renderTag(mapAudit[value].label, mapAudit[value].color)
    }
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 150
  },
  {
    title: '业务部门',
    dataIndex: 'operation_department',
    width: 150,
    resizable: true
  },
  {
    title: '应收金额',
    dataIndex: 'receivable_left',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUnDef(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '实收金额',
    dataIndex: 'received_actual',
    width: 100,
    resizable: true,
    helpMessage: '实收金额与已收金额不对等,应是本张销售单存在退货或退款金额',
    customRender: ({ value }) => {
      return isUnDef(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '结算金额',
    dataIndex: 'audit_amount',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUnDef(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '佣金',
    dataIndex: 'commission',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isUnDef(value) || isNull(value) ? '-' : formateerNotCurrency.format(value)
    }
  },
  {
    title: '方案负责人',
    dataIndex: 'program_incharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '项目负责人',
    dataIndex: 'inCharge_name',
    width: 150,
    resizable: true
  },
  {
    title: '结束日期',
    dataIndex: 'est_finished_at',
    width: 150,
    resizable: true
  },
  {
    title: '结算时间',
    dataIndex: 'audit_at',
    width: 150,
    resizable: true
  }
]
const status_schema = GET_STATUS_SCHEMA(saleStore.mapOrderStatusOptions)
export const searchFormSchema: FormSchema[] = [
  status_schema,
  DIVIDER_SCHEMA,
  {
    field: 'project_name',
    label: '项目名称',
    component: 'Input'
  },
  {
    field: 'project_number',
    label: '项目id',
    component: 'Input'
  },
  {
    field: 'operation',
    label: '业务部门',
    component: 'PagingApiSelect',
    componentProps: {
      api: getDept,
      // params: { status: 1, is_audit: 1, is_operate: 1 },
      params: { status: 1, is_audit: 1 },
      resultField: 'items',
      labelField: 'name',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'name'
        },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        style: {
          width: '100%'
        }
      }
    }
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        showSearch: true,
        optionFilterProp: 'name',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: saleStore.mapOrderStatusOptions
  //   },
  //   colProps: { span: 8 }
  // },
  {
    field: 'source_uniqid',
    label: '销售订单号',
    component: 'Input'
  },
  {
    field: 'program_incharge',
    label: '方案负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'inCharge',
    label: '项目负责人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      immediate: true,
      lazyLoad: true,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'is_audit',
    label: '结算状态',
    component: 'RadioButtonGroup',
    componentProps: {
      options: [
        { label: '未完成结算', value: 0 },
        { label: '已完成结算', value: 1 }
      ]
    }
  },
  {
    field: 'receivable',
    label: '应收金额',
    component: 'Input',
    slot: 'receivable'
  },
  {
    field: 'submited_at',
    label: '开单时间',
    component: 'SingleRangeDate',
    // defaultValue: [dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],

    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'collection_at',
    label: '收款时间',
    component: 'SingleRangeDate',
    // defaultValue: [dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],

    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'receipt_num',
    label: '收款次数',
    component: 'InputNumber',
    componentProps: {
      step: 1,
      precision: 0
    }
  },
  {
    field: 'bind_fund',
    label: '流水绑定',
    component: 'SingleRangeDate',
    // defaultValue: [dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],

    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'doc_fund_created_at',
    label: '创建日期',
    component: 'SingleRangeDate',
    // defaultValue: [dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],

    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    }
  }
]

export const childColumns: (reload?) => BasicColumn[] = (reload?) => [
  {
    title: '收款单号',
    dataIndex: 'strid',
    width: 120,
    align: 'left'
  },
  {
    title: '收款日期',
    dataIndex: 'collection_at',
    width: 120,
    align: 'left'
  },
  {
    title: '紧急状态',
    dataIndex: 'urgent_level',
    width: 120,
    align: 'left',
    helpMessage: '未关联流水可以点击修改紧急状态！',
    customRender: ({ text, record }) => {
      return text ? urgentLevel(record.id, text, record.status == 0, 1, reload) : '-'
    }
  },
  {
    title: '应收金额',
    dataIndex: 'amount_ans',
    width: 120,
    resizable: true,
    align: 'left',
    customRender: ({ value }) => {
      return value ? formateerNotCurrency.format(value) : '0.00'
    }
  },
  {
    title: '本次已收金额',
    dataIndex: 'amount_rec',
    width: 120,
    align: 'left',
    customRender: ({ value }) => {
      return value ? formateerNotCurrency.format(value) : '0.00'
    }
  },
  {
    title: '款项类型',
    dataIndex: 'payment_type',
    width: 120,
    align: 'left',
    customRender: ({ value, record }) => {
      if ((isNull(value) || isUnDef(value)) && (isNull(record.rfw_payment_type) || isUnDef(record.rfw_payment_type))) return ''
      const payment_type = record.rfw_payment_type ? record.rfw_payment_type : value
      return useRender.renderTag(mapValue[payment_type].text)
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    align: 'left',
    customRender: ({ value }) => {
      return isNull(value) || isUnDef(value) ? '' : useRender.renderTag(mapStatus[value].text, mapStatus[value].color)
    }
  },
  {
    title: '审核状态',
    dataIndex: 'is_check',
    width: 100,
    resizable: true,
    customRender({ text }) {
      return isNull(text) || isUnDef(text) ? '' : text < 2 ? '正常' : '驳回'
    }
  },
  {
    title: '流水绑定日期',
    dataIndex: 'bind_fund_created_at',
    width: 200,
    resizable: true
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    width: 200,
    resizable: true
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 150
  }
]
