<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" showFooter width="50%" destroyOnClose title="回访用语">
    <a
      href="https://img.gbuilderchina.com/erp/purchase/20250604/2025060416072450388/1749932960683ffebcaca4b326741501.xlsx"
      style="color: red; font-size: 20px; font-weight: bold; text-decoration: underline"
      target="_blank"
    >
      回访话术大纲-中英v1.6(点击下载)
    </a>
    <Descriptions bordered :column="1" :labelStyle="{ width: '140px' }">
      <DescriptionsItem label="首次建群">
        <div>
          Hello,here is George Building Material after-sales service group,I am your customer service Minny.We will synchronize our project
          progress every week,and you can also check the progress through the following link
        </div>
        <div
          :class="{
            list: true,
            ' mt-4': true
          }"
        >
          <div v-if="props && props.project_number">
            Project Number: <span class="text-red-600">{{ props.project_number }}</span>
          </div>
          <div>
            Quality inspection overview link:
            <span class="text-red-600 cursor-pointer" @click="copyText(copyProjectQCLink())"
              ><copy-outlined class="mr-1" />{{ copyProjectQCLink() }}
            </span>
          </div>
          <div class="my-2">
            Quality inspection overview link(product):
            <span class="text-red-600 cursor-pointer" @click="copyText(copyProjectProductQCLink())"
              ><copy-outlined class="mr-1" />{{ copyProjectProductQCLink() }}
            </span>
          </div>
          <div class="my-2">
            Quality inspection overview link (is not for client)(product):
            <span class="text-red-600 cursor-pointer" @click="copyText(copyProjectProductQCLinkSelf())"
              ><copy-outlined class="mr-1" />{{ copyProjectProductQCLinkSelf() }}
            </span>
          </div>
          <div
            >Project satisfaction link:
            <span class="cursor-pointer text-blue-400" @click="copyText(copyShareevaluation())"
              ><copy-outlined />{{ copyShareevaluation() }}
            </span>
          </div>
        </div>
      </DescriptionsItem>
      <DescriptionsItem label="每周维护">
        <template v-if="work_list.length > 0">
          <div
            :class="{
              list: true,
              'mt-4': index !== 0
            }"
            v-for="(item, index) in work_list"
            :key="item.id"
          >
            <div>
              Order Number: <span class="text-red-600">{{ item.source_uniqid }}</span>
            </div>

            <div class="my-2"
              >QC report link:
              <span class="text-red-600 cursor-pointer" @click="copyText(copyLinkCalc(item))"
                ><copy-outlined />{{ copyLinkCalc(item) }}
              </span>
            </div>
            <div class="my-2"
              >QC report link(product):
              <span class="text-red-600 cursor-pointer" @click="copyText(copyLinkCalcProduct(item))"
                ><copy-outlined />{{ copyLinkCalcProduct(item) }}
              </span>
            </div>
            <div
              >Progress link:
              <span class="cursor-pointer text-blue-400" @click="copyText(copyProgressLine(item))"
                ><copy-outlined />{{ copyProgressLine(item) }}
              </span>
            </div>
          </div></template
        >
        <div class="mt-4">
          Remarks: Some orders may take a long time to prepare raw materials, so it seems that there is no process, which is normal.If you
          have any questions, please feel free to consult us.
        </div>
      </DescriptionsItem>
      <DescriptionsItem label="全部订单出库"
        >Hello,You order have delivered and finished loading the container.When you recevied the products please contact with us.Thank you
        and have a good day.</DescriptionsItem
      >
    </Descriptions>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CopyOutlined } from '@ant-design/icons-vue'
import { Descriptions, DescriptionsItem } from 'ant-design-vue'

import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { encryptByBase64 } from '/@/utils/cipher'
import { copyText } from '/@/utils/copyTextToClipboard'
import { ENV_URLS } from '/@/utils/envUrls'

interface WorkItem {
  id: number | string
  source_uniqid: string
  client_id: string | number
}

const props = ref<any>() // Consider defining a more specific type for props if possible
const work_list = ref<WorkItem[]>([])
const [registerDrawer, { changeLoading }] = useDrawerInner(async (data) => {
  try {
    await changeLoading(true)
    props.value = data
    console.log(props.value.client_id)

    work_list.value = data.work
  } finally {
    changeLoading(false)
  }
})

function copyProjectQCLink() {
  if (props.value && props.value.project_number && props.value.project_name) {
    return ENV_URLS.PROJECTQCLINK + encryptByBase64(`project_number=${props.value.project_number}&project_name=${props.value.project_name}`)
  }
  return ''
}

function copyProjectProductQCLink() {
  if (props.value && props.value.project_number && props.value.client_id) {
    return (
      ENV_URLS.PROJECTPRODUCTQCLINK + encryptByBase64(`project_number=${props.value.project_number}&client_id=${props.value.client_id}`)
    )
  }
  return ''
}
//员工自用
function copyProjectProductQCLinkSelf() {
  if (props.value && props.value.project_number && props.value.client_id) {
    return (
      ENV_URLS.PROJECTPRODUCTQCLINK_SELF +
      encryptByBase64(`project_number=${props.value.project_number}&client_id=${props.value.client_id}`)
    )
  }
  return ''
}

//根据销售单号查找
function copyLinkCalcProduct(item: WorkItem) {
  if (props.value && props.value.project_number && item.client_id) {
    return (
      ENV_URLS.LINKCALC_PRODUCT +
      encryptByBase64(`project_number=${props.value.project_number}&client_id=${item.client_id}&source_uniqid=${item.source_uniqid}`)
    )
  }
  return ''
}

function copyLinkCalc(item: WorkItem) {
  return `${ENV_URLS.LINKCALC}${encryptByBase64(item.source_uniqid)}&netnet=${encryptByBase64(
    String(item.client_id) // Ensure client_id is a string for encryption
  )}`
}
function copyProgressLine(item: WorkItem) {
  return `${ENV_URLS.PROGRESSLINE}source_uniqid=${encryptByBase64(item.source_uniqid)}`
}
function copyShareevaluation() {
  if (props.value) {
    return `https://erp.gbuilderchina.com/satisfactionSharetest/#/satisfactionShare?client_id=${encryptByBase64(
      props.value.client_id
    )}&project_number=${encryptByBase64(props.value.project_number)}`
  }
  return ''
}
</script>
