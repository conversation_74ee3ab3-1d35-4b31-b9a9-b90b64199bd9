<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd" v-if="hasPermission([721])">新增</Button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editDrawer @register="registereditDrawer" @success="reload" />
  </div>
</template>
<script setup lang="ts">
import { columns, schemas } from './datas/datas'
import { messageconfiggetList, messageconfigsetIsDisabled } from '/@/api/baseData/messagepushtemplate'
import { BasicTable, useTable, TableAction, EditRecordRow, ActionItem } from '/@/components/Table'
import { Button } from 'ant-design-vue'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'

const { hasPermission } = usePermission()
const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [registerTable, { reload }] = useTable({
  showIndexColumn: false,
  showTableSetting: true,
  useSearchForm: true,
  columns,
  api: messageconfiggetList,
  formConfig: {
    labelWidth: 120,
    schemas
  },
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right'
  }
})

function createActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      label: '启用',
      onClick: handledisabled.bind(null, record, 'qing'),
      disabled: record.is_disabled == 0,
      ifShow: hasPermission([723]) && record.is_disabled == 1
    },
    {
      label: '禁用',
      onClick: handledisabled.bind(null, record, 'jiny'),
      disabled: record.is_disabled == 1,
      ifShow: hasPermission([724]) && record.is_disabled == 0
    },
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      disabled: hasPermission([722]) && record.is_disabled == 0
    }
  ]
}

function createDropDownActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      label: '详情',
      onClick: handleDetail.bind(null, record)
    }
  ]
}

function handleAdd() {
  openDrawer(true, { type: 'add' })
  setDrawerProps({ title: '新增信息推送模版', showFooter: true })
}
function handleDetail(record) {
  openDrawer(true, { record, type: 'detail' })
  setDrawerProps({ title: '信息推送模版详情', showFooter: false })
}
function handleEdit(record) {
  openDrawer(true, { record, type: 'edit' })
  setDrawerProps({ title: '编辑信息推送模版', showFooter: true })
}

async function handledisabled(record, type) {
  try {
    await messageconfigsetIsDisabled({ id: record.id, is_disabled: type == 'qing' ? 0 : 1 })
    await reload()
  } catch (e) {
    console.log(e)
  }
}
</script>
