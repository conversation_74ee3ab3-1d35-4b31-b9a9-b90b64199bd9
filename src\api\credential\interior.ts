import { defHttp } from '/@/utils/http/axios'

enum Api {
  getList = '/erp/cert/ia/getList',
  //创建
  checkOut = '/erp/cert/ia/checkOut',
  //审核
  setinterStatus = '/erp/cert/ia/setStatus',
  //审核
  setinterdelete = '/erp/cert/ia/delete'
}

export const getList = (params?: {}) => defHttp.get({ url: Api.getList, params })

export const checkOut = (params: {}) =>
  defHttp.get({ url: Api.checkOut, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

export const setinterStatus = (params: {}) =>
  defHttp.get({ url: Api.setinterStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })

export const setinterdelete = (params: {}) =>
  defHttp.get({ url: Api.setinterdelete, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
