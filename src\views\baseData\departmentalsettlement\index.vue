<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleCreate" v-if="hasPermission([751])"> 新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <editDrawer @register="registereditDrawer" @success="reload" />
  </div>
</template>
<script setup lang="ts">
import { dadelete, dagetList } from '/@/api/baseData/departmentalsettlement'
import { BasicTable, useTable, TableAction, ActionItem, EditRecordRow } from '/@/components/Table'
import { Button } from 'ant-design-vue'
import editDrawer from './components/editDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { columns } from './datas/data'
import { usePermission } from '/@/hooks/web/usePermission'

const { hasPermission } = usePermission()

const [registereditDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [registerTable, { reload }] = useTable({
  showIndexColumn: false,
  api: dagetList,
  showTableSetting: true,
  columns,
  actionColumn: {
    width: 230,
    title: '操作',
    dataIndex: 'action'
  }
})

function createActions(record: EditRecordRow): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([201])
    },
    {
      icon: 'clarity:note-edit-line',
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      ifShow: hasPermission([752])
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        placement: 'left',
        confirm: handleDelete.bind(null, record)
      },
      ifShow: hasPermission([753])
    }
  ]

  return editButtonList
}

function handleCreate() {
  openDrawer(true, {
    type: 'add'
  })
  setDrawerProps({ title: '新增', showFooter: true, width: '40%' })
}

function handleDetail(record) {
  openDrawer(true, { type: 'detail', record })
  setDrawerProps({ title: '详情', showFooter: false, width: '40%' })
}
function handleEdit(record) {
  openDrawer(true, {
    record,
    type: 'edit'
  })
  setDrawerProps({ title: '编辑', showFooter: true, width: '40%' })
}
async function handleDelete(record) {
  await dadelete({ id: record.id })
  reload()
}
</script>
