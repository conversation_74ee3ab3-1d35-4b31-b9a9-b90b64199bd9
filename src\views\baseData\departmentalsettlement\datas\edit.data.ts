import { getDeptTree } from '/@/api/admin/dept'
import { FormSchema } from '/@/components/Form'

export const schemas: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false
  },
  {
    field: 'dept_id',
    label: '部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: true,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        placeholder: '请选择',
        treeDefaultExpandAll: true,
        showSearch: true,
        optionFilterProp: 'name',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    required: true
  },
  {
    field: 'is_rec_audit',
    label: '是否收款后结算',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 2
        },
        {
          label: '否',
          value: 1
        }
      ]
    }
  },
  {
    field: 'is_audit_allot',
    label: '结算后是否费用调拨',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 2
        },
        {
          label: '否',
          value: 1
        }
      ]
    }
  }
]
