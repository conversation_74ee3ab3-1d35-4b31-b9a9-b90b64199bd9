<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="新增" show-footer @ok="handleSubmit" width="50%">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { schemas } from '../datas/drawer'
import { checkOut } from '/@/api/credential/interior'
// checkOutpl
const emit = defineEmits(['success', 'register'])

const [registerDrawer, { closeDrawer, changeOkLoading }] = useDrawerInner(async () => {
  resetFields()
})

const [registerForm, { resetFields, validate }] = useForm({
  schemas,
  baseColProps: { span: 12 },
  showActionButtonGroup: false,
  colon: true,
  labelCol: { style: { width: '70px' } }
})

async function handleSubmit() {
  try {
    changeOkLoading(true)
    const valid = await validate()
    console.log(valid)
    await checkOut(valid)
    emit('success')
    closeDrawer()
    changeOkLoading(false)
  } catch (e) {
    changeOkLoading(false)
    console.log(e)
  }
}
</script>
