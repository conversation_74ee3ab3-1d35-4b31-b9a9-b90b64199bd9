<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button v-if="hasPermission([387])" @click="handleDelete" type="primary">删除</Button>
        <Button v-if="hasPermission([388])" @click="handleEdit" type="primary">审核</Button>
        <!-- <Button v-if="hasPermission([386])" @click="handletoleadDetail" type="primary">新增</Button> -->
        <Button @click="handletoleadDetail" type="primary">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <Tag :color="statustype[record.status]?.color"> {{ statustype[record.status]?.label }}</Tag>
        </template>
      </template>
    </BasicTable>
    <addlossDrawer @register="registerDrawer" @success="reload" />
    <editeModal @register="registerModal" @success="reload" />
  </div>
</template>
<script setup lang="ts" name="/credential/credential">
import { useTable, BasicTable } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'
import { columns, schemas, statustype } from './datas/datas'
import addlossDrawer from './components/addlossDrawer.vue'
import { Tag, Button } from 'ant-design-vue'
import editeModal from './components/editeModal.vue'
import { useModal } from '/@/components/Modal'
import { getList } from '/@/api/credential/interior'

const { hasPermission } = usePermission()

const [registerTable, { reload }] = useTable({
  title: '带单部门内部调拨账目',
  api: getList,
  columns,
  showTableSetting: true,
  rowKey: 'id',
  showIndexColumn: false,
  useSearchForm: true,
  formConfig: {
    labelWidth: 120,
    schemas,
    autoSubmitOnEnter: true,
    baseColProps: { span: 8 }
  },
  pagination: {
    pageSize: 10,
    pageSizeOptions: ['10', '50', '100'],
    position: ['bottomRight']
  },
  rowSelection: {
    type: 'checkbox'
  }
})

//编辑
const [registerDrawer, { openDrawer: openEditDrawer }] = useDrawer()
function handletoleadDetail(record: any) {
  openEditDrawer(true, record)
}

// 注册modal
const [registerModal, { openModal, setModalProps }] = useModal()
//审核
async function handleEdit() {
  openModal(true, 'status')
  setModalProps({ title: '审核' })
}
//删除
async function handleDelete() {
  openModal(true, 'delete')
  setModalProps({ title: '删除' })
}
</script>
