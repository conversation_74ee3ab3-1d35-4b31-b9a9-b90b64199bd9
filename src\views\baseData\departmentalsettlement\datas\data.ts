import { h } from 'vue'
import { BasicColumn } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'

export const columns: BasicColumn[] = [
  {
    title: '部门名称',
    dataIndex: 'department',
    width: 120
  },
  {
    title: '结算后是否费用调拨',
    dataIndex: 'is_audit_allot',
    width: 120,
    customRender({ text }) {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text == 1 ? 'red' : 'green' }, text == 1 ? '否' : '是')
    }
  },
  {
    title: '是否收款后结算',
    dataIndex: 'is_rec_audit',
    width: 120,
    customRender({ text }) {
      return isNullOrUnDef(text) ? '-' : h(Tag, { color: text == 1 ? 'red' : 'green' }, text == 1 ? '否' : '是')
    }
  }
]
