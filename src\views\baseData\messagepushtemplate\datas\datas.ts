import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { getDeptTree } from '/@/api/admin/dept'
import { getStaffList } from '/@/api/erp/systemInfo'
import { getAccountList } from '/@/api/commonUtils'
import { useRender } from '/@/components/Table/src/hooks/useRender'

export const columns: BasicColumn[] = [
  {
    dataIndex: 'creator_name',
    title: '创建人',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'department',
    title: '所属部门',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'penson_names',
    title: '消息通知人员',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : useRender.renderTags(text)
    }
  },
  {
    dataIndex: 'is_disabled',
    title: '是否禁用',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : text !== '' ? h(Tag, { color: text == 0 ? 'green' : 'red' }, text == 0 ? '否' : '是') : '-'
    }
  },
  {
    dataIndex: 'created_at',
    title: '创建日期',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'remark',
    title: '备注',
    width: 100,
    resizable: true
  }
]

export const schemas: FormSchema[] = [
  // {
  //   field: 'title',
  //   label: '费用科目',
  //   component: 'Input',
  //   colProps: { span: 8 }
  // },
  // {
  //   field: 'account_code',
  //   label: '支出科目',
  //   component: 'PagingApiSelect',
  //   componentProps: {
  //     api: getCategory,
  //     resultField: 'items',
  //     labelField: 'account_name',
  //     valueField: 'account_name',
  //     selectProps: {
  //       fieldNames: {
  //         key: 'key',
  //         value: 'account_code',
  //         label: 'account_name'
  //       },
  //       showSearch: true,
  //       placeholder: '请选择',
  //       optionFilterProp: 'account_name',
  //       allowClear: true
  //     },
  //     params: {
  //       status: 1
  //     }
  //   },
  //   colProps: { span: 8 }
  // },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getAccountList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      pagingSize: 20,
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    colProps: { span: 8 }
  },
  {
    field: 'dept_id',
    label: '所属部门',
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptTree,
      immediate: false,
      lazyLoad: true,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    },
    colProps: { span: 8 }
  },
  {
    field: 'is_disabled',
    label: '是否禁用',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    colProps: { span: 8 }
  },
  {
    field: 'penson_ids',
    label: '消息通知人员',
    component: 'PagingApiSelect',
    itemProps: {
      validateTrigger: 'blur'
    },
    componentProps: {
      api: getStaffList,
      resultField: 'items',
      searchMode: true,
      pagingMode: true,
      selectProps: {
        mode: 'multiple',
        allowClear: true,
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name'
      }
    },
    colProps: { span: 8 }
  }
]
