<template>
  <BasicDrawer @register="registerDrawer" width="90%" @ok="handleOk" destroyOnClose @close="handleClose">
    <BasicForm @register="registerForm" />
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button type="primary" @click="handleAdd" :disabled="currentEditKeyRef !== ''">新增</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { columns, schemas } from '../datas/edit.data'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { ActionItem, BasicTable, EditRecordRow, useTable, TableAction } from '/@/components/Table'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'
import { messageconfigupdate } from '/@/api/baseData/messagepushtemplate'

//保存点击,其他禁用
const currentEditKeyRef = ref('')
const emit = defineEmits(['success'])

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  console.log(data)
  resetFields()
  setTableData([])
  resetSchema(schemas(data.type))
  setColumns(columns)
  if (data.type !== 'add') {
    await setFieldsValue(data.record)
    setTableData(data.record.message_config_item)
  }
})

const [registerForm, { validate, setFieldsValue, resetSchema, resetFields }] = useForm({
  // schemas,
  labelWidth: 140,
  baseColProps: { span: 24 },
  showActionButtonGroup: false
})
const [registerTable, { setTableData, getDataSource, getColumns, updateTableDataRecord, setColumns, deleteTableDataRecord }] = useTable({
  // columns,
  showIndexColumn: false,
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action'
  }
})
function handleAdd() {
  const newRowDataItem = {
    account_code: undefined,
    amount_max: 0,
    content: '于{{date}}，{{deptId1}}收取{{deptId2}}{{accountName}}{{amount}}元。如有疑问联系{{deptId1}}负责人或财务部。'
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

//action
function createActions(record: EditRecordRow): ActionItem[] {
  if (!record.editable) {
    return [
      {
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        // ifShow: record.is_check2 !== 2 && !record.is_cancel,
        ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handleEdit.bind(null, record)
      },
      {
        color: 'error',
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          placement: 'left',
          confirm: handleDelete.bind(null, record)
        },
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        ifShow: record.is_check2 !== 2 && !record.is_cancel
      },
      {
        label: '复制',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        ifShow: record.is_check2 !== 2 && !record.is_cancel,
        onClick: handlecopylink.bind(null, record)
      }
    ]
  }
  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record)
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        placement: 'right',
        confirm: handleCancel.bind(null, record)
      }
    }
  ]
}

//格式化某一行的数据
function formatObject(record) {
  // getDataSource()有我们不需要的属性,所以需要清除
  let temporary = {}
  for (let colName of getColumns()) {
    if (colName.key !== 'action') {
      temporary[`${colName.dataIndex}`] = record[`${colName.dataIndex}`]
    }
  }
  return temporary
}
// 格式化提交的数据
function formatSubmit() {
  // getDataSource()有我们不需要的属性,所以需要清除
  const dataSource = getDataSource().map((item) => {
    let temporary = {}
    for (let colName of getColumns()) {
      // if (colName.key !== 'action') {
      if (!['account_name', 'action'].includes(colName.key)) {
        temporary[`${colName.dataIndex}`] = item[`${colName.dataIndex}`]
      }
    }
    return temporary
  })
  return dataSource
}

// 存储编辑前的record
const beforeRecord = ref()
async function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  beforeRecord.value = cloneDeep(record)
  record.onEdit?.(true, false)
}

//删除tabel
async function handleDelete(record) {
  deleteTableDataRecord(record.key)
}

//复制明细
function handlecopylink(record) {
  const newrecord = formatObject(record)
  delete newrecord.id
  const newRowDataItem = {
    ...newrecord
  }
  const dataSource = [newRowDataItem].concat(cloneDeep(getDataSource()))
  setTableData(dataSource)
}

//保存
async function handleSave(record: EditRecordRow) {
  // 校验
  const valid = await record.onValid?.()
  if (valid) {
    try {
      if (!record.title) {
        message.error({ content: '请填写通知标题' })
        return
      }
      if (!record.account_name) {
        message.error({ content: '请填写科目' })
        return
      }
      if (!record.amount_max) {
        message.error({ content: '请填写金额' })
        return
      }

      //TODO 此处将数据提交给服务器保存
      const datas = getDataSource()
      const data = datas.filter((item) => item.account_code == record.account_code)
      if (data.length > 1) {
        message.error({ content: '科目不能重复' })
        return
      }
      // 保存之后提交编辑状态
      // ...
      // 保存之后提交编辑状态

      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      message.success({ content: '数据已保存', key: 'saving' })
    } catch (error) {
      message.error({ content: '保存失败', key: 'saving' })
      throw new Error(`${error}`)
    }
  } else {
    message.error({ content: '请填写正确的数据', key: 'saving' })
  }
}

//取消
function handleCancel(record: EditRecordRow) {
  currentEditKeyRef.value = ''
  updateTableDataRecord(record.key, {
    amount_max: beforeRecord.value.amount_max,
    account_code: beforeRecord.value.account_code
  })

  record.onEdit?.(false, false)
}

//提交
async function handleOk() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    const data = await formatSubmit()
    if (data.length == 0) return message.error('请填写明细')
    await messageconfigupdate({ ...formdata, items: data })
    console.log(data, formdata)
    changeOkLoading(false)
    closeDrawer()
    emit('success')
  } catch (error) {
    console.log(error)
  } finally {
    changeOkLoading(false)
  }
}

function handleClose() {
  closeDrawer()
  currentEditKeyRef.value = ''
}
</script>
<style lang="less" scoped></style>
