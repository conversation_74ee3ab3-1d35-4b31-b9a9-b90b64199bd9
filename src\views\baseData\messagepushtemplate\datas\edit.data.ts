import { getDept } from '/@/api/erp/systemInfo'
import { getStaffList } from '/@/api/baseData/staff'
import { getCategory } from '/@/api/financialDocuments/otherIncome'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'

export const schemas = (type: string): FormSchema[] => {
  return [
    {
      field: 'id',
      label: 'id',
      component: 'Input',
      show: false
    },
    {
      field: 'dept_id',
      label: '所属部门',
      required: true,
      component: 'PagingApiSelect',
      itemProps: {
        validateTrigger: 'blur'
      },
      colProps: { span: 8 },
      componentProps: {
        api: getDept,
        resultField: 'items',
        labelField: 'name',
        valueField: 'id',
        searchMode: true,
        pagingMode: true,
        returnParamsField: 'id',
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          style: {
            width: '100%'
          }
        }
      },
      dynamicDisabled: ['detail'].includes(type)
    },
    {
      field: 'penson_ids',
      label: '消息通知人员',
      required: true,
      component: 'PagingApiSelect',
      itemProps: {
        validateTrigger: 'blur'
      },
      componentProps: {
        api: getStaffList,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        returnParamsField: 'id',
        selectProps: {
          mode: 'multiple',
          allowClear: true,
          maxTagCount: 3,
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name'
        }
      },
      colProps: { span: 8 },
      dynamicDisabled: ['detail'].includes(type)
    },
    {
      field: 'is_disabled',
      label: '是否禁用',
      component: 'Select',
      required: true,
      defaultValue: 0,
      componentProps: {
        options: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      },
      dynamicDisabled: ['detail'].includes(type),
      colProps: { span: 8 }
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea',
      colProps: { span: 8 },
      dynamicDisabled: ['detail'].includes(type)
    }
  ]
}

export const columns: BasicColumn[] = [
  {
    title: '通知标题',
    dataIndex: 'title',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'Input',
    editComponentProps: {
      placeholder: '请输入'
    }
  },
  {
    title: '科目',
    dataIndex: 'account_name',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'ApiSelect',
    editComponentProps: ({ record }) => {
      return {
        api: getCategory,
        resultField: 'items',
        selectProps: {
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          // labelInValue: true,
          fieldNames: { account_code: 'account_code', value: 'account_name', label: 'account_name' },
          optionFilterProp: 'account_name',
          onChange(_, shall) {
            console.log(record)
            if (!shall) {
              record.account_code = undefined
            }
            record.account_code = shall.account_code
          }
        }
      }
    },
    // 默认必填校验
    editRule: true
  },
  {
    title: '科目id',
    dataIndex: 'account_code',
    defaultHidden: true
  },
  {
    title: '金额阈值',
    dataIndex: 'amount_max',
    width: 200,
    resizable: true,
    editRow: true,
    editComponent: 'InputNumber',
    editComponentProps: {
      precision: 2,
      min: 0.01
    }
  },
  {
    title: '通知内容',
    dataIndex: 'content',
    width: 250,
    resizable: true,
    editRow: true,
    editComponent: 'Textarea',
    editComponentProps: {
      placeholder: '请输入',
      rows: 5
    },
    editDynamicDisabled: true
  }
]
