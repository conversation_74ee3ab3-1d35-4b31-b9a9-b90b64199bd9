<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    destroyOnClose
    width="90%"
    :closable="false"
    :bodyStyle="{
      backgroundColor: '#e6e4e4'
    }"
    :headerStyle="{
      display: 'none'
    }"
  >
    <div class="container">
      <template v-if="propData">
        <div class="box" id="box">
          <div class="top_bar">
            <div class="left">
              <QrCode :value="computedQrCode" :width="300" :logo="ScanImg" />
              <div class="qrcode_tit">
                <div class="tit">仓库扫码/入库二维码</div>
                <div class="qrcode_tit en">Warehouse QR code</div>
              </div>
            </div>
            <div class="right">
              <div class="logo">
                <img :src="LogoImg" alt="" />
              </div>
              <template v-if="propData.page === 'purchase'">
                <div class="pkg">
                  <div class="tit">包装号：</div>
                  <div>Packing No.</div>
                  <div class="content break">{{ computedStrid }}</div>
                </div>
              </template>
              <!-- <div class="type">
                <div class="tit">包装数量：</div>
                <div class="tit">Packing Quantity</div>
                <div class="content">{{ propData.pkg }}</div>
              </div> -->
              <div class="type">
                <div class="tit">产品类别：</div>
                <div class="content break">{{ propData.record.dept_type }}</div>
              </div>

              <div class="pkg_num">
                <div class="tit">包装数量：</div>
                <div class="tit">Packing Quantity</div>
                <div class="content num">{{ propData.pkg }}</div>
              </div>
            </div>
          </div>

          <!-- 中间表格部分start -->
          <div class="middle_bar">
            <div class="left">
              <div class="strid">
                <div class="tit">订单号：</div>
                <div class="tit">Ordere No.</div>
                <div class="content">{{ propData.record.source_uniqid }}</div>
              </div>
              <div class="sprite">
                <img :src="SpriteImg" alt="" />
              </div>
            </div>
            <div class="right">
              <!-- <QrCode value="JC12122343434" :width="300" /> <div class="qrcode_tit">
                <div class="title">客户扫码查看装箱单</div>
                <div class="qrcode_tit en">Scan to check packing list</div>
              </div> -->
            </div>
          </div>
          <!-- 中间表格部分end -->

          <div class="bottom_bar">
            <div class="tit"> 佛山市禅城区季华四路意美家卫浴陶瓷世界24栋 </div>
            <div class="en"
              >24 block CASA ceramics & sanitary ware market Jihua 4 road, Chancheng district, Foshan city, Guangdong China.
            </div>
          </div>
        </div>

        <div class="btn_list">
          <a-button @click="debounceHanldPrint" type="primary" class="mr-4">预览 </a-button>
          <a-button @click="debounceHanldPrintToPDF" type="primary" ghost class="mr-4">导出pdf</a-button>
          <a-button @click="closeDrawer">取消</a-button>
        </div>
      </template>
    </div>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { useLoading } from '/@/components/Loading'
import { debounce } from 'lodash-es'
import printJS from 'print-js'
import { QrCode } from '/@/components/Qrcode'

import LogoImg from '/@/assets/images/jc_logo.jpg'
import SpriteImg from '/@/assets/images/sprite.png'
import ScanImg from '/@/assets/images/scan.png'

const propData = ref()

const [registerDrawer, { changeLoading, closeDrawer }] = useDrawerInner((data) => {
  try {
    propData.value = data
    changeLoading(true)

    changeLoading(false)
  } catch (e) {
    console.log(e)
  } finally {
    changeLoading(false)
  }
})

const [openFullLoading, closeFullLoading] = useLoading({
  tip: '加载中...'
})

const computedStrid = computed(() => {
  return propData.value?.record?.purchase_strid.split('-')[0]
})

const computedQrCode = computed(() => {
  const propDataValue = propData.value
  return `JC/${propDataValue.record.source_uniqid};JC/${
    propDataValue.page === 'saleOrder' ? propDataValue.record.source_uniqid : propDataValue.record.purchase_strid
  };${propDataValue.pkg}`
})

const debounceHanldPrint = debounce(handlePrint, 500)
async function handlePrint() {
  openFullLoading()

  const arr = []

  const imageToPrint = document.getElementById('box')
  await captureAndPushImageToArr(imageToPrint, arr)

  await delay(1000) // 等待一段时间确保图片加载完成
  closeFullLoading()

  printJS({
    printable: arr,
    type: 'image',
    imageStyle: 'width:100%;'
  })
}

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

async function captureAndPushImageToArr(imageToPrint, arr, type = 'pdf') {
  // 动态导入 html2canvas 包
  const html2canvas = await import('html2canvas')
  const canvas = await html2canvas.default(imageToPrint, {
    dpi: type === 'print' ? 150 : 160, //分辨率
    scale: 1.8,
    useCORS: true //允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求
    // scrollY: imageToPrint.offsetHeight // 关键代码，截取长度
  })
  const imageData = canvas.toDataURL('image/png')
  const img = new Image()
  img.src = imageData
  arr.push(img.src)
  canvas.remove()
}

const debounceHanldPrintToPDF = debounce(handlePrintToPDF, 500)
async function handlePrintToPDF() {
  openFullLoading()

  const arr = []

  const imageToPrint = document.getElementById('box')
  await captureAndPushImageToArr(imageToPrint, arr)

  await delay(1000) // 等待一段时间确保图片加载完成
  closeFullLoading()
  // 动态导入 jsPDF 包
  const { default: jsPDF } = await import('jspdf')
  // 将图片数组转换为 PDF
  const pdf = new jsPDF({
    unit: 'mm',
    format: [85, 85] // 设置纸张大小为60*60mm
  })
  for (let i = 0; i < arr.length; i++) {
    pdf.addImage(arr[i], 'PNG', 0, 0, 85, 85) // 使用75*75mm纸张大小
    if (i !== arr.length - 1) {
      pdf.addPage()
    }
  }

  pdf.save('exported.pdf')
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  height: 100%;
  width: 100%;
}
.box {
  //   font-size: 12px;
  width: 945px;
  height: 945px;
  box-sizing: border-box;
  background-color: rgb(255, 255, 255);
  padding: 0 0 27px;
  .content {
    font-size: 40px;
    font-weight: 800;
  }
  //   background-image: url(../../../assets/images/print-bg-img.jpg);
  //   background-size: cover;
  .top_bar {
    display: flex;

    width: 100%;
    padding: 0px 20px;

    .left {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 300px;
      .qrcode_tit {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 24px;
        font-weight: bold;
        &.en {
          font-size: 20px;
        }
      }
    }
    .right {
      position: relative;
      padding-top: 45px;
      font-size: 24px;
      font-weight: 600;
      flex: 1;
      .pkg {
        height: 120px;
        margin-top: 90px;
      }
      .type {
        margin-top: 5px;
        // width: 400px;
      }
      .break {
        width: 400px;
        word-break: break-all;
        line-height: 40px;
      }
      .logo {
        width: 450px;
        margin-top: -30px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .pkg_num {
        position: absolute;
        right: 10px;
        top: 120px;

        .num {
          margin-top: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 185px;
          height: 185px;
          box-sizing: border-box;
          border: 1px solid #000;
          border-radius: 5px;
          font-size: 70px;
        }
      }
    }
  }
  .middle_bar {
    display: flex;
    border: 5px solid #000;
    margin: 15px 30px 15px;
    height: 370px;
    box-sizing: border-box;

    .left {
      display: flex;
      flex-direction: column;
      border-right: 3px solid #000;
      height: 100%;
      width: 505px;
      box-sizing: border-box;

      .strid {
        padding: 5px;
        width: 100%;
        height: 310px;
        border-bottom: 3px solid #000;
        font-size: 24px;
        font-weight: bold;
        .content {
          font-size: 70px;
        }
      }
      .sprite {
        display: flex;
        align-items: center;
        flex: 1;
        img {
          width: 100%;
          height: 80%;
        }
      }
    }
    .right {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .qrcode_tit {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 24px;
        font-weight: bold;
        &.en {
          font-size: 20px;
        }
      }
    }
  }
  .bottom_bar {
    font-size: 23px;
    font-weight: bold;
    width: 100%;
    padding: 0 13px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    .en {
      font-size: 20px;
    }
  }
}
.btn_list {
  margin-left: 20px;
}
</style>
