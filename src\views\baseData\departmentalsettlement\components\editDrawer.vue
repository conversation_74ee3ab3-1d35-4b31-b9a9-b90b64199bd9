<template>
  <BasicDrawer @register="registerDrawer" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { schemas } from '../datas/edit.data'
import { dacreateOrUpdate } from '/@/api/baseData/departmentalsettlement'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
const emit = defineEmits(['registerDrawer', 'success'])

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner((data) => {
  console.log(data)
  resetFields()
  setFieldsValue(data.record)
})

const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
  schemas,
  labelWidth: 140,
  actionColOptions: { span: 24 },
  baseColProps: { span: 24 },
  colon: true,
  showActionButtonGroup: false
})

async function handleSubmit() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    await dacreateOrUpdate(formdata)
    changeOkLoading(false)
    closeDrawer()
    emit('success')
  } catch (e) {
    changeOkLoading(false)
    console.log(e)
  }
}
</script>
