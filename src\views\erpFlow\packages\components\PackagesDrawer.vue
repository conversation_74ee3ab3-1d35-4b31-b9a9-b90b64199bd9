<template>
  <div>
    <BasicDrawer @register="registerDrawer" v-bind="$attrs" width="100%" destroy-on-close @ok="handleOk" @close="handleClose">
      <Row :gutter="20" type="flex">
        <template v-if="propsData.type !== 'split'">
          <Col span="24" class="form-wrap">
            <div style="color: red; font-size: 20px; font-weight: bolder; margin-bottom: 20px; padding-left: 15px">
              只有采购订单的产品生产完成并且未装包裹数大于0
              才可以创建包裹,如果没有生产完成请先前往"采购商品跟踪"进行勾选产品生产完成再创建包裹（请仔细检查所有产品是否已完成生产）</div
            >
            <BasicForm @register="registerForm" />
          </Col>
          <Col span="24">
            <Divider />
          </Col>
        </template>
        <Col flex="1">
          <Row :gutter="20" type="flex" align="middle">
            <Col flex="1" class="no-padding">
              <div class="flex justify-between mb-2 wrap-title">
                <div class="title"> 产品清单 </div>
                <div>
                  <a-button type="primary" danger @click="handleSelectUseCount" class="mr-2">设置最大数量</a-button>
                  <a-button type="primary" @click="handleSelectAll" class="mr-2">选中所有</a-button>
                  <a-button type="primary" @click="handleSelectUseCountProduct" class="mr-2">选中可使用数量大于0</a-button>
                  <a-button type="default" @click="selectProduct = []">清空选中</a-button>
                </div>
              </div>
              <div class="border-1 border-[#f0f0f0] h-55vh">
                <div class="overflow-y-scroll h-full" v-if="compProductList.length > 0">
                  <CheckboxGroup v-model:value="selectProduct" class="w-full">
                    <Draggable
                      :list="compProductList"
                      :group="{ name: 'productList', pull: 'clone', put: false }"
                      item-key="key"
                      :clone="cloneDog"
                      :sort="false"
                      :move="handleProductMove"
                      :handle="['.move-icon', '.move-main-img', '.move-main-title']"
                      @start="isLightHeight = true"
                      @end="isLightHeight = false"
                    >
                      <template #item="{ element: child }">
                        <div class="w-full !mb-3 !p-5 !ml-0 checkbox-item rounded-lg flex">
                          <Checkbox :value="child.key" class="flex-1 !flex !items-center">
                            <div class="w-full">
                              <div class="flex w-full">
                                <div class="w-110px h-110px flex justify-center items-center move-main-img">
                                  <Image
                                    width="100%"
                                    height="100%"
                                    :src="child.imgs?.[0] || '//img.gbuilderchina.com/erp/20240701/172061451866827c20e822f444948727.png'"
                                  />
                                </div>
                                <div class="flex-1 flex flex-col justify-between product-info pl-2 pr-2">
                                  <div class="flex justify-between move-main-title">
                                    <div class="flex-1 info-title">
                                      <div>{{ child.name }} - 项目ID：{{ child.projectNumber }}</div>
                                      <div class="text-xs text-[#999]">{{ child.puid }}</div>
                                    </div>
                                  </div>
                                  <div v-show="propsData.type === 'add'" class="goods-input flex flex-wrap">
                                    <div class="inline-block w-[160px] mb-1 mr-1">
                                      <InputNumber
                                        v-model:value="child.length"
                                        addonBefore="长"
                                        addonAfter="CM"
                                        size="small"
                                        min="0.01"
                                        @change="handleSyncPackageInfo(child)"
                                      />
                                    </div>

                                    <div class="inline-block w-[160px] mb-1 mr-1">
                                      <InputNumber
                                        v-model:value="child.width"
                                        addonBefore="宽"
                                        addonAfter="CM"
                                        size="small"
                                        min="0.01"
                                        @change="handleSyncPackageInfo(child)"
                                      />
                                    </div>

                                    <div class="inline-block w-[160px] mb-1 mr-1">
                                      <InputNumber
                                        v-model:value="child.height"
                                        addonBefore="高"
                                        addonAfter="CM"
                                        size="small"
                                        min="0.01"
                                        @change="handleSyncPackageInfo(child)"
                                      />
                                    </div>

                                    <div>
                                      <Input
                                        class="!w-[130px] mr-1"
                                        v-model:value="child.material"
                                        addonBefore="材质"
                                        size="small"
                                        @change="handleSyncPackageInfo(child)"
                                      />
                                      <Input
                                        class="!w-[130px]"
                                        v-model:value="child.code"
                                        addonBefore="海关码"
                                        size="small"
                                        @change="handleSyncPackageInfo(child)"
                                      />
                                    </div>
                                  </div>
                                  <div class="info-desc">描述：{{ child.desc || '-' }}</div>
                                </div>
                              </div>
                            </div>
                          </Checkbox>
                          <div class="flex justify-center items-end flex-col">
                            <div class="info-count">
                              <InputNumber
                                v-model:value="child.buildQuantity"
                                :max="sub(+child.maxQuantity, compPackageProductCount[child.key] ?? 0)"
                                prefix="×"
                                :precision="child.key.includes('child') ? 0 : 2"
                                size="small"
                                :min="child.key.includes('child') ? 1 : 0.01"
                              />
                            </div>
                            <div class="text-base text-[#999] mt-1">
                              可使用数量：
                              <span class="text-[#87d068] font-bold">
                                {{ sub(+child.maxQuantity, compPackageProductCount[child.key] ?? 0) }}
                              </span>
                            </div>
                            <div class="mt-2 flex">
                              <div class="mr-1 split-packages-input">
                                <Popover
                                  v-model:visible="showPopoverGather[child.key]"
                                  placement="left"
                                  trigger="click"
                                  @visible-change="handleShowSplitPopover"
                                >
                                  <template #content>
                                    <div class="inline-block w-[200px] mr-1">
                                      <InputNumber
                                        v-model:value="splitGoodsCount"
                                        placeholder="请输入数值"
                                        addon-before="生成包裹数"
                                        size="small"
                                        :max="
                                          child.key.includes('child')
                                            ? sub(+child.maxQuantity, compPackageProductCount[child.key] ?? 0)
                                            : null
                                        "
                                        :precision="0"
                                        min="1"
                                      />
                                    </div>
                                    <a-button type="primary" size="small" @click="handleSplitGoods(child)">确定</a-button>
                                  </template>
                                  <template #title>
                                    <div>可用数量&gt;1的商品按输入数量生成包裹</div>
                                    <div>可用数量=1的商品，最大输入数为 100</div>
                                    <div>可用数量&lt;1的商品，最大输入数为 可使用数量*100</div>
                                  </template>
                                  <a-button type="primary" size="small">等份拆分</a-button>
                                </Popover>
                              </div>

                              <div class="move-icon">
                                <Tooltip placement="left">
                                  <template #title>按住此处，可以将产品拖动到包裹高亮处，可将当前设置数量的产品拖动包裹中</template>
                                  <!--                                <Icon icon="ic:sharp-menu" size="20" />-->
                                  <a-button type="primary" size="small">拖动产品</a-button>
                                </Tooltip>
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </Draggable>
                    <!--                    </div>-->
                  </CheckboxGroup>
                </div>
                <div v-else class="h-full flex justify-center items-center">
                  <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                </div>
              </div>
              <div class="flex justify-between w-full mt-2">
                <div>
                  <Tooltip>
                    <template #title>按输入的数值，将勾选的产品生成到一个包裹</template>
                    <a-button type="primary" class="mr-2" @click="handleAddPackage">创建包裹</a-button>
                  </Tooltip>
                </div>
                <div>
                  <Dropdown class="mr-2">
                    <template #overlay>
                      <Menu>
                        <MenuItem key="code">
                          <Popover
                            title="对已勾选的商品批量设置海关码"
                            placement="top"
                            trigger="click"
                            @visible-change="() => (batchGoodsCode = '')"
                          >
                            <template #content>
                              <div class="inline-block w-[200px] mr-1">
                                <Input v-model:value="batchGoodsCode" size="small" placeholder="请输入海关码" />
                              </div>
                              <a-button size="small" type="primary" @click="handleBatchSet('code', batchGoodsCode)">确定</a-button>
                            </template>
                            <span>批量设置海关码</span>
                          </Popover>
                        </MenuItem>
                        <MenuItem key="code">
                          <Popover
                            title="对已勾选的商品批量设置材质"
                            placement="top"
                            trigger="click"
                            @visible-change="() => (batchGoodsMaterial = '')"
                          >
                            <template #content>
                              <div class="inline-block w-[200px] mr-1">
                                <Input v-model:value="batchGoodsMaterial" size="small" placeholder="请输入材质" />
                              </div>
                              <a-button size="small" type="primary" @click="handleBatchSet('material', batchGoodsMaterial)">确定</a-button>
                            </template>
                            <span>批量设置材质</span>
                          </Popover>
                        </MenuItem>
                      </Menu>
                    </template>
                    <a-button type="primary">
                      批量设置
                      <DownOutlined />
                    </a-button>
                  </Dropdown>
                  <Tooltip>
                    <template #title>按勾选每个产品的1个数量，生成多个包裹</template>
                    <a-button type="primary" class="mr-2" @click="handleGenderMinPackage">最少单位生成包裹</a-button>
                  </Tooltip>
                  <Tooltip>
                    <template #title> 按勾选每个产品的最大可使用数量生成单独包裹 </template>
                    <a-button type="primary" class="mr-2" @click="handleGenderUsePackage">按商品可用数量生成包裹</a-button>
                  </Tooltip>
                  <Tooltip>
                    <template #title>将勾选的产品按最大可使用数量，生成到同一个包裹中</template>
                    <a-button type="primary" @click="handleGenderOnlyPackage">生成单一包裹</a-button>
                  </Tooltip>
                </div>
              </div>
            </Col>
          </Row>
        </Col>
        <Col>
          <Row type="flex" align="middle" class="h-full">
            <Col>
              <div class="w-30px h-30px border rounded-full flex justify-center items-center opacity-40">
                <SwapRightOutlined />
              </div>
            </Col>
          </Row>
        </Col>
        <Col span="12">
          <div class="flex justify-between mb-2 wrap-title">
            <div class="title"> 包裹列表 </div>
            <div>
              <Popover
                class="mr-2"
                title="对符合条件的包裹进行批量写入包裹备注"
                placement="bottomRight"
                trigger="click"
                @visible-change="() => (batchPackageRemark = '')"
              >
                <template #content>
                  <div class="inline-block w-[600px] mr-1">
                    <Textarea
                      v-model:value="batchPackageRemark"
                      class="resize-none"
                      :rows="2"
                      size="small"
                      placeholder="请输入批量设置的包裹备注"
                    />
                  </div>
                  <a-button size="small" type="primary" class="mr-2" @click="handleSetRemark(false)">设置空备注的包裹</a-button>
                  <a-button size="small" type="primary" @click="handleSetRemark(true)">设置所有包裹备注</a-button>
                </template>
                <a-button type="primary">批量设置包裹备注</a-button>
              </Popover>
              <Tooltip class="mr-2">
                <template #title>将包裹中只有一种产品，且数量等于1的产品信息（长、宽、高、重量、体积）同步到包裹</template>
                <a-button @click="handleSyncInfoToPackages">同步包裹信息</a-button>
              </Tooltip>
              <a-button type="primary" class="mr-2" @click="isProductSimple = !isProductSimple"
                >{{ isProductSimple ? '展开' : '收起' }}产品信息</a-button
              >
              <a-button type="primary" @click="isPackageSimple = !isPackageSimple"
                >{{ isPackageSimple ? '展开' : '收起' }}包裹信息</a-button
              >
            </div>
          </div>
          <div class="h-60vh no-padding border-1 border-[#f0f0f0]">
            <div class="overflow-y-scroll h-full" v-if="packageList.length > 0">
              <div
                v-for="(pkg, idx) in packageList"
                :key="idx"
                class="bg-[#F4F5F5] w-99% package-product-wrap box-border m-2"
                :data-key="pkg.key"
                :ref="(el) => packageListRef.push(el)"
              >
                <div class="package-name mb-2 flex justify-between items-center">
                  <div>包裹{{ pkg.key }} - 项目ID：{{ pkg.projectNumber }}</div>
                  <a-button type="error" size="small" @click="handleRemovePackage(idx)">删除包裹</a-button>
                </div>
                <Draggable
                  :data-packageIndex="idx"
                  :sort="false"
                  :list="pkg.product"
                  group="productList"
                  item-key="itemKey"
                  :class="{ 'border border-blue-500': isLightHeight }"
                  :handle="['.product-name']"
                  :move="handlePackageMove"
                  @change="(data) => handleChange(data, idx)"
                >
                  <template #item="{ element: product }">
                    <Tag color="blue" class="!mb-2 inline-block">
                      <div>
                        <div class="product flex items-center p-1">
                          <div class="product-name mb-1 mt-1 flex flex-col">
                            <div>产品名称：{{ product.name }}</div>
                            <div class="text-xs text-[#999]">产品编码：{{ product.puid }}</div>
                          </div>
                          <!--  <div v-show="isProductSimple" class="product-count ml-3 text-blue-500">×{{ product.buildQuantity }}</div>-->
                          <div v-show="isProductSimple" class="info-count ml-1">
                            <InputNumber
                              v-model:value="product.buildQuantity"
                              :max="add(sub(+product.maxQuantity, compPackageProductCount[product.key] ?? 0), product.buildQuantity)"
                              prefix="×"
                              size="small"
                              :precision="product.key.includes('child') ? 0 : 2"
                              :min="product.key.includes('child') ? 1 : 0.01"
                              @change="(val) => handleCountChange(val, product)"
                            />
                          </div>
                          <a-button size="small" class="ml-1" type="default" @click="handleRemovePackageProduct(idx, product)"
                            >删除</a-button
                          >
                        </div>
                        <div class="product-form">
                          <Form
                            v-show="!isProductSimple"
                            :ref="(el) => (productFormEl[product.itemKey] = el)"
                            :key="product.itemKey"
                            :model="mapPackageProduct[product.itemKey]"
                            :name="`product-${product.itemKey}`"
                            :label-col="{ style: { width: `${productFormConfig.labelWidth}px` } }"
                          >
                            <Row>
                              <Col
                                :span="goodsSchemas.colProps?.span || productFormConfig.baseColProps.span"
                                v-for="goodsSchemas in productFormSchemas"
                                :key="goodsSchemas.field"
                              >
                                <FormItem
                                  :label="goodsSchemas.label"
                                  :name="goodsSchemas.field"
                                  :rules="[{ required: goodsSchemas.required, message: `请输入${goodsSchemas.label}` }]"
                                >
                                  <template v-if="goodsSchemas.field === 'buildQuantity'">
                                    <component
                                      :is="goodsSchemas.component"
                                      v-model:value="mapPackageProduct[product.itemKey][goodsSchemas.field]"
                                      placeholder="请输入"
                                      :max="
                                        add(
                                          sub(+mapPackageProduct[product.itemKey].maxQuantity, compPackageProductCount[product.key] ?? 0),
                                          mapPackageProduct[product.itemKey].buildQuantity
                                        )
                                      "
                                      :precision="product.key.includes('parent') ? 2 : 0"
                                      :min="product.key.includes('parent') ? 0.01 : 1"
                                      v-bind="goodsSchemas.componentProps"
                                      :disabled="goodsSchemas.dynamicDisabled"
                                    />
                                  </template>
                                  <template v-else>
                                    <component
                                      :is="goodsSchemas.component"
                                      v-model:value="mapPackageProduct[product.itemKey][goodsSchemas.field]"
                                      placeholder="请输入"
                                      v-bind="goodsSchemas.componentProps"
                                      :disabled="goodsSchemas.dynamicDisabled"
                                    />
                                  </template>
                                </FormItem>
                              </Col>
                            </Row>
                          </Form>
                          <!--                          <BasicForm-->
                          <!--                            v-show="!isProductSimple"-->
                          <!--                            :ref="(el) => (productFormEl[product.itemKey] = el)"-->
                          <!--                            :key="product.itemKey"-->
                          <!--                            v-bind="productFormConfig"-->
                          <!--                            v-model:model="mapPackageProduct[product.itemKey]"-->
                          <!--                          />-->
                        </div>
                      </div>
                    </Tag>
                  </template>
                </Draggable>
                <div>
                  <Form
                    v-show="!isPackageSimple"
                    :ref="(el) => setPackagesFormEl(el, idx, pkg)"
                    :model="packageList[idx]"
                    :name="`package-${pkg.key}`"
                    :label-col="{ style: { width: `${formConfig.labelWidth}px` } }"
                  >
                    <Row>
                      <Col
                        :span="schemas.colProps?.span || formConfig.baseColProps.span"
                        v-for="schemas in formSchemas"
                        :key="schemas.field"
                      >
                        <template v-if="schemas.field === 'info'">
                          <Divider orientation="left">{{ schemas.label }}</Divider>
                        </template>
                        <template v-else-if="['width', 'height', 'length'].includes(schemas.field)">
                          <FormItem
                            :label="schemas.label"
                            :name="schemas.field"
                            :rules="[{ required: schemas.required, message: `请输入${schemas.label}` }]"
                          >
                            <component
                              :is="schemas.component"
                              v-model:value="packageList[idx][schemas.field]"
                              min="0.01"
                              v-bind="schemas.componentProps"
                              @change="handleSetVolume(idx)"
                              :disabled="schemas.dynamicDisabled"
                              placeholder="请输入"
                            />
                          </FormItem>
                        </template>
                        <template v-else>
                          <FormItem
                            :label="schemas.label"
                            :name="schemas.field"
                            :rules="[{ required: schemas.required, message: `请输入${schemas.label}` }]"
                          >
                            <component
                              :is="schemas.component"
                              v-model:value="packageList[idx][schemas.field]"
                              v-bind="schemas.componentProps"
                              :disabled="schemas.dynamicDisabled"
                              placeholder="请输入"
                            />
                          </FormItem>
                        </template>
                      </Col>
                    </Row>
                  </Form>
                  <!--                  <BasicForm-->
                  <!--                    v-show="!isPackageSimple"-->
                  <!--                    v-bind="formConfig"-->
                  <!--                    :ref="(el) => setPackagesFormEl(el, idx, pkg)"-->
                  <!--                    :key="pkg.key"-->
                  <!--                    v-model:model="packageList[idx]"-->
                  <!--                  />-->
                </div>
              </div>
            </div>
            <div v-else class="h-full flex justify-center items-center">
              <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
            </div>
          </div>
        </Col>
        <Col span="24">
          <div>
            <BasicForm @register="registerPackageInfo" />
          </div>
        </Col>
      </Row>
    </BasicDrawer>
  </div>
</template>

<script setup lang="ts">
import { useDrawerInner, BasicDrawer } from '/@/components/Drawer'
import {
  Row,
  Col,
  Tag,
  Divider,
  CheckboxGroup,
  Checkbox,
  Image,
  InputNumber,
  Tooltip,
  Empty,
  Input,
  Popover,
  Form,
  Dropdown,
  MenuItem,
  Menu,
  Textarea
} from 'ant-design-vue'
// import ScrollContainer from '/@/components/Container/src/ScrollContainer.vue'
import { BasicForm, useForm } from '/@/components/Form'
import { ref, h, withDirectives, watch } from 'vue'
import { SwapRightOutlined, DownOutlined } from '@ant-design/icons-vue'
import { getPurchaseDetail } from '/@/api/erp/purchaseOrder'
import { getRelatePurchaseList } from '/@/api/erp/inWarehouse'
import { cloneDeep, difference, random, pick } from 'lodash-es'
import { add, div, mul, sub } from '/@/utils/math'
import { useMessage } from '/@/hooks/web/useMessage'
import loadingDirective from '/@/directives/loading'
import Draggable from 'vuedraggable'
// import { Icon } from '/@/components/Icon'
import {
  formConfig,
  packageFormEl,
  productFormConfig,
  packageList,
  selectProduct,
  purchaseProductList,
  compProductList,
  compSelectProduct,
  compPackageProductCount,
  mapPackageProduct,
  productFormEl,
  compPackageProduct,
  mapProductForPackage,
  mapPackageProductForKey,
  mapProductList,
  mapPackageFormEl,
  formSchemas,
  productFormSchemas,
  packagesFormSchemas,
  enableBaseInfo
} from '../datas/drawer.data'
import { addPackage } from '/@/api/erpFlow/packages'
import Decimal from 'decimal.js'
// import { useIntersectionObserver } from '@vueuse/core'

const emits = defineEmits(['success', 'register', 'split'])
const loading = ref<boolean>(false)
const { createMessage, notification } = useMessage()
const pkgFlag = ref(1) // 包裹的序号生成标识
const curOrderIds = ref([]) // 选中的采购订单id
const isLightHeight = ref<boolean>(false) // 拖拽时是否高亮
const isProductSimple = ref<boolean>(true) // 是否收起/展开填写信息
const isPackageSimple = ref<boolean>(false) // 是否收起/展开填写信息
const propsData = ref({})
const packageListRef = ref([])
const splitGoodsCount = ref(null)
const showPopoverGather = ref({})

const FormItem = Form.Item

const batchGoodsCode = ref('')
const batchGoodsMaterial = ref('')
const batchPackageRemark = ref('')
// const showPkgForm = ref({})
// const stopArr = ref([])

const [registerDrawer, { closeDrawer, changeLoading, changeOkLoading }] = useDrawerInner((data) => {
  propsData.value = data
  const { type, packageInfo } = data
  // console.log(type, packageInfo, useInfo)
  const mapTypeHandler = {
    add: () => {},
    split: handleSplitInit
  }
  mapTypeHandler[type]?.(packageInfo)
})

const [registerForm] = useForm({
  size: 'small',
  showActionButtonGroup: false,
  submitButtonOptions: { size: 'small' },
  schemas: [
    {
      field: 'doc_id',
      component: 'PagingApiSelect',
      label: '采购订单',
      itemHelpMessage:
        '只有采购订单的产品生产完成并且未装包裹数大于0 才可以创建包裹,如果没有生产完成请先前往"采购商品跟踪"进行勾选产品生产完成再创建包裹',
      componentProps: {
        resultField: 'items',
        api: (params) => getRelatePurchaseList({ ...params, is_qty_pkg_left: 1 }),
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        searchParamField: 'strid',
        allowClear: false,
        selectProps: {
          mode: 'multiple',
          allowClear: true,
          fieldNames: { value: 'doc_id', label: 'strid' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'strid',
          dropdownRender: ({ menuNode }) => {
            const vNode = h('div', {}, menuNode)

            return withDirectives(vNode, [[loadingDirective, loading.value]])
          }
        },
        params: {
          type: 4,
          is_production_finish: 1
        },
        onChange(val) {
          if (curOrderIds.value.length < val.length) {
            // 增加选项
            const [cur] = difference(val, curOrderIds.value)
            // 直营搜索
            handleSearchSubmit(cur)
          } else if (curOrderIds.value.length > val.length) {
            // 去除选项
            const [doc_id] = difference(curOrderIds.value, val)
            // 如果取消的选项已经被勾选，也需要将选中状态移除
            const removeDocId = compSelectProduct.value.filter((item) => item.doc_id === doc_id).map((prod) => prod.key)
            if (removeDocId.length > 0) {
              selectProduct.value = selectProduct.value.filter((item) => !removeDocId.includes(item))
            }
            // 从产品清单中移除
            purchaseProductList.value = purchaseProductList.value.filter((item) => item.doc_id !== doc_id)
          }

          // 最后更新历史记录
          curOrderIds.value = val
        }
      }
    }
  ],
  labelWidth: 80,
  baseColProps: { span: 24 },
  // actionColOptions: { span: 8 },
  autoSubmitOnEnter: true
  // submitFunc: handleSearchSubmit
})

const [registerPackageInfo, { getFieldsValue, setFieldsValue, updateSchema }] = useForm({
  showActionButtonGroup: false,
  layout: 'inline',
  labelWidth: 70,
  baseColProps: { span: 2 },
  schemas: packagesFormSchemas
})

watch(
  () => enableBaseInfo.value,
  () => {
    updateSchema(packagesFormSchemas)
  }
)

function isSameProject(goodsList) {
  return [...new Set(goodsList.map((item) => item.projectNumber))].length === 1
}

function handleSetVolume(idx) {
  const { length, width, height } = packageList.value[idx]
  packageList.value[idx].volume = div(mul(mul(length ?? 0, width ?? 0), height ?? 0), 1000000, 6)
}

function handleSplitInit(packageInfo) {
  purchaseProductList.value = cloneDeep(packageInfo.product).map((item) => ({
    ...item,
    key: `split-${item.prodKey}`,
    maxQuantity: item.quantity,
    buildQuantity: item.quantity,
    item_sub: [],
    projectNumber: item.project_number
    // purchase_work_id: 0
  }))
  setFieldsValue({ ...(propsData.value?.useInfo || {}) })
}

function handleBatchSet(field, value) {
  for (const key of selectProduct.value) {
    mapProductList.value[key][field] = value
    handleSyncPackageInfo(mapProductList.value[key])
  }
}

function handleSetRemark(isAll: boolean) {
  for (const packages of packageList.value) {
    packages.remark = !isAll && packages.remark ? packages.remark : batchPackageRemark.value
  }
}

// 提交搜索
async function handleSearchSubmit(id) {
  try {
    loading.value = true
    // const data = await validate()
    // const { items } = await getSalesOrderListReq(data)
    // const { items } = await getPurchaseDetail(data)
    const { items, doc } = await getPurchaseDetail({ doc_id: id, pageSize: 1000, status: 2 })
    const list =
      items?.map((item) => {
        const leftCount = +item.qty_pkg_left ?? 0
        // 统计是否存在当前包裹中，如果在包裹中，则用可使用数量 - 包裹订单数
        const parentMaxQuantity = compPackageProductCount[`parent-${item.id}`]
          ? sub(leftCount, compPackageProductCount[`parent-${item.id}`] ?? 0)
          : leftCount ?? 0

        const itemsSub =
          item?.items_sub?.map((child) => {
            const childLeftCount = +child.qty_pkg_left ?? 0
            // 统计是否存在当前包裹中，如果在包裹中，则用可使用数量 - 包裹订单数
            const childMaxQuantity = compPackageProductCount[`child-${child.id}`]
              ? sub(childLeftCount, compPackageProductCount[`child-${child.id}`] ?? 0)
              : childLeftCount

            return {
              ...child,
              maxQuantity: childMaxQuantity,
              buildQuantity: childMaxQuantity,
              key: `child-${child.id}`,
              purchase_work_id: doc.work_id,
              projectNumber: doc.project_number || '-'
            }
          }) ?? []

        return {
          ...item,
          key: `parent-${item.id}`,
          maxQuantity: parentMaxQuantity,
          buildQuantity: parentMaxQuantity,
          items_sub: itemsSub,
          purchase_work_id: doc.work_id,
          projectNumber: doc.project_number || '-'
        }
      }) ?? []

    purchaseProductList.value = purchaseProductList.value.concat(list)
  } catch (err) {
    console.log(err)
    createMessage.error('获取采购订单列表失败')
  } finally {
    loading.value = false
  }
}

// 创建单个包裹
function handleAddPackage() {
  const errMsg = validSelectProductGender('input')
  if (errMsg) return createMessage.error(errMsg)
  const isPass = isSameProject(compSelectProduct.value)
  if (!isPass) return createMessage.error('不同项目的产品不允许生成同一个包裹')
  const [goods] = compSelectProduct.value
  packageList.value.push({
    // ...(propsData.value?.useInfo || {}),
    ...(enableBaseInfo.value ? getFieldsValue() || {} : { length: 0.01, width: 0.01, height: 0.01 }),
    key: pkgFlag.value,
    product: cloneDeep(compSelectProduct.value).map((item) => ({ ...item, itemKey: random(1, 1000000000) })),
    projectNumber: goods.projectNumber
  })
  pkgFlag.value += 1
}

// 拖拽目标的数据
function cloneDog(data) {
  return cloneDeep({ ...data, itemKey: random(1, 1000000000) })
}

// 产品清单的产品拖拽触发
function handleProductMove(evt) {
  const { draggedContext, to } = evt
  const { element } = draggedContext
  const { maxQuantity, key, buildQuantity } = element
  // 判断是不是同一个项目，同一个项目才可以执行后面的代码
  const toPackageIdx = to.dataset.packageindex
  const toPackage = packageList.value[toPackageIdx]
  // 判断这个商品的项目id跟包裹的项目id是否一致
  const isSameProjectNumber = element.projectNumber === toPackage.projectNumber

  if (!isSameProjectNumber) {
    const msg = notification
    msg.config({ maxCount: 1 })
    msg.error({ message: '失败', description: '不同项目的产品不可放到同一个包裹中！' })
    return isSameProjectNumber
  }

  // 正常判断商品的数量
  const useCount = sub(+maxQuantity, compPackageProductCount.value[key] ?? 0)

  const arrowDrag = useCount > 0 && useCount >= buildQuantity
  if (!arrowDrag) {
    const msg = notification
    msg.config({ maxCount: 1 })
    msg.error({ message: '失败', description: '商品的可使用数量不足！' })
  }
  return arrowDrag
}

function handlePackageMove(evt) {
  const { from, to } = evt
  const fromPackageIdx = from.dataset.packageindex
  const fromPackage = packageList.value[fromPackageIdx]
  const toPackageIdx = to.dataset.packageindex
  const toPackage = packageList.value[toPackageIdx]

  const isSameProjectNumber = fromPackage.projectNumber === toPackage.projectNumber
  if (!isSameProjectNumber) {
    const msg = notification
    msg.config({ maxCount: 1 })
    msg.error({ message: '失败', description: '不同项目的产品不可放到同一个包裹中！' })
  }
  return isSameProjectNumber
}

// 移除包裹
function handleRemovePackage(idx) {
  // const pkgIdx = packageList.value.findIndex((item) => item.key === packages.key)
  // if (pkgIdx === -1) return createMessage.error('没有找到该包裹！')
  packageList.value.splice(idx, 1)
  if (packageList.value.length === 0) pkgFlag.value = 1
}

// 移除包裹中的产品
function handleRemovePackageProduct(pkgIdx, product) {
  if (packageList.value[pkgIdx]?.product?.length <= 1) return createMessage.error('包裹的最后一个商品不允许删除')
  packageList.value[pkgIdx].product = packageList.value[pkgIdx].product.filter((item) => product.key !== item.key)
}

// 监听包裹产品拖拽后发生的变化
function handleChange(data, pkgIdx) {
  const { added, removed } = data

  // 产品清单的产品移动到包裹列表触发
  if (added) {
    const { newIndex, element } = added
    // 检查是否已经存在这个商品，如果存在就合并
    const originProductIdx = packageList.value[pkgIdx].product.findIndex((item, idx) => item.key === element.key && idx !== newIndex)
    if (originProductIdx !== -1) {
      packageList.value[pkgIdx].product[originProductIdx].buildQuantity = add(
        packageList.value[pkgIdx].product[originProductIdx].buildQuantity,
        element.buildQuantity
      )
      packageList.value[pkgIdx].product.splice(newIndex, 1)
    }
  }

  // 不同包裹互相移动会触发，需要判断包裹是否为空，包裹为空则移除包裹
  if (removed) {
    if (packageList.value[pkgIdx].product.length === 0) {
      packageList.value.splice(pkgIdx, 1)
    }
  }
}

// 按每个产品的最少值生成一个包裹
function handleGenderMinPackage() {
  let addPackageList: Array<{ key: number; product: any[] }> = []
  if (selectProduct.value.length <= 0) return createMessage.error('请先选择产品清单的商品')
  const hasEmpty = compSelectProduct.value.some((item) => {
    const useCount = sub(+item.maxQuantity, compPackageProductCount.value[item.key])
    return useCount < 0.01
  })
  if (hasEmpty) return createMessage.error('商品的可使用数量小于1，无法创建包裹')
  for (const product of compSelectProduct.value) {
    let useCount = sub(+product.maxQuantity, compPackageProductCount.value[product.key])
    const countArr: number[] = []
    while (useCount > 0) {
      if (useCount >= 1) {
        countArr.push(1)
        useCount--
      } else {
        countArr.push(useCount)
        useCount = 0
      }
    }
    const packagesArr = countArr.map((count) => {
      return {
        // ...(propsData.value?.useInfo || {}),
        ...(enableBaseInfo.value ? getFieldsValue() || {} : { length: 0.01, width: 0.01, height: 0.01 }),
        key: pkgFlag.value++,
        product: [{ ...cloneDeep(product), buildQuantity: count, itemKey: random(1, 1000000000) }],
        projectNumber: product.projectNumber
      }
    })
    addPackageList = addPackageList.concat(packagesArr)
    // addPackageList.push({
    //   ...(propsData.value?.useInfo || {}),
    //   key: pkgFlag.value,
    //   product: [{ ...cloneDeep(product), buildQuantity: 1, itemKey: random(1, 1000000000) }]
    // })
    // pkgFlag.value += 1
  }
  packageList.value = packageList.value.concat(addPackageList)
}

// 按可使用的最大商品数量生成包裹
function handleGenderUsePackage() {
  const addPackageList: Array<{ key: number; product: any[]; projectNumber: number | string }> = []
  const errMsg = validSelectProductGender('useCount')
  if (errMsg) return createMessage.error(errMsg)
  for (const product of compSelectProduct.value) {
    const useCount = sub(+product.maxQuantity, compPackageProductCount.value[product.key])
    addPackageList.push({
      // ...(propsData.value?.useInfo || {}),
      ...(enableBaseInfo.value ? getFieldsValue() || {} : { length: 0.01, width: 0.01, height: 0.01 }),
      key: pkgFlag.value,
      product: [{ ...cloneDeep(product), buildQuantity: useCount, itemKey: random(1, 1000000000) }],
      projectNumber: product.projectNumber
    })
    pkgFlag.value += 1
  }
  packageList.value = packageList.value.concat(addPackageList)
}

// 按最大可使用数量生成一个包裹
function handleGenderOnlyPackage() {
  const isPass = isSameProject(compSelectProduct.value)
  if (!isPass) return createMessage.error('不同项目的产品不允许生成同一个包裹')
  const errMsg = validSelectProductGender('useCount')
  if (errMsg) return createMessage.error(errMsg)
  const [goods] = compSelectProduct.value
  packageList.value.push({
    // ...(propsData.value?.useInfo || {}),
    ...(enableBaseInfo.value ? getFieldsValue() || {} : { length: 0.01, width: 0.01, height: 0.01 }),
    key: pkgFlag.value,
    product: cloneDeep(compSelectProduct.value).map((item) => {
      const useCount = sub(+item.maxQuantity, compPackageProductCount.value[item.key])
      return { ...item, buildQuantity: useCount, itemKey: random(1, 1000000000) }
    }),
    projectNumber: goods.projectNumber
  })
  pkgFlag.value += 1
}

// 验证选中的产品是否有可使用数量，会返回一个errMsg
// type：useCount - 验证可使用数量
// type：input - 验证输入数量
function validSelectProductGender(type: 'useCount' | 'input' = 'input') {
  if (selectProduct.value.length <= 0) return '请先选择产品清单的商品'
  const hasEmpty = compSelectProduct.value.some((item) => {
    const useCount = sub(+item.maxQuantity, compPackageProductCount.value[item.key])
    if (type === 'input') return useCount <= 0 || item.buildQuantity > useCount || item.buildQuantity <= 0
    else return useCount <= 0
  })
  if (hasEmpty) return '商品的可使用数量不足或输入数量不合法，无法创建包裹'
  return ''
}

// 选中可使用数量大于0的产品
function handleSelectUseCountProduct() {
  const selectList: string[] = []
  for (const product of compProductList.value) {
    const hasUseCount = sub(+product.maxQuantity, compPackageProductCount.value[product.key] ?? 0) > 0
    if (hasUseCount) selectList.push(product.key)
  }
  if (selectList.length === 0) createMessage.error('所有商品的可使用数量均为0')
  selectProduct.value = selectList
}

// 左边选中所有
function handleSelectAll() {
  const selectList: number[] = []
  for (const prod of compProductList.value) {
    selectList.push(prod.key)
  }
  selectProduct.value = selectList
}

function handleSelectUseCount() {
  for (const prod of selectProduct.value) {
    mapProductList.value[prod].buildQuantity = sub(mapProductList.value[prod].maxQuantity ?? 0, compPackageProductCount.value[prod] ?? 0)
  }
}

async function handleBeforeSubmit() {
  // let validResult = true
  let errKey: number | string | null = null
  try {
    // 验证包裹信息
    for (const packages of packageList.value) {
      errKey = packages.key
      const packageForm = mapPackageFormEl.value[packages.key]
      await packageForm.validate()
      // const { formActionType } = packageForm
      // const { validate: validatePackage } = formActionType
      // await validatePackage()
    }
  } catch (err) {
    console.log(err)
    createMessage.error(`包裹${errKey}信息未完善！`)
    // validResult = false
    return false
  }

  try {
    // 验证产品的填写信息
    for (const product of compPackageProduct.value) {
      errKey = product.itemKey
      const productForm = productFormEl.value[product.itemKey]
      // const { formActionType } = productForm
      // const { validate: validateProduct } = formActionType
      await productForm.validate()
    }
  } catch (err) {
    const errProduct = mapPackageProduct.value[errKey]
    const errPkg = mapProductForPackage.value[errKey]
    createMessage.error(`包裹${errPkg.key} - ${errProduct.name}商品的信息未填写`)
    // validResult = false
    return false
  }
  return true
  // return validResult
}

function getParams() {
  console.log(packageList.value)
  return {
    packageList: packageList.value.map((packages) => ({
      ...packages,
      purchase_work_ids: [...new Set(packages.product.map((item) => item.purchase_work_id))],
      sale_work_ids: [...new Set(packages.product.map((item) => item.sale_work_id).flat())].filter((item) => item),
      items: packages.product.map((product) => ({
        ...pick(product, [
          'name',
          'material',
          'quantity',
          'unit',
          'code',
          'remark',
          'puid',
          'request_id',
          'purchase_work_id',
          'supplier_strid'
        ]),
        imgs: product.imgs ?? [],
        quantity: product.buildQuantity,
        request_sub_id: product.request_sub_id ? product.request_sub_id : void 0,
        type: product.key.includes('child') ? 2 : 1,
        item_purchase_id: product.key.includes('child') ? product.item_purchase_id : product.purchase_id,
        item_purchase_sub_id: product.key.includes('child') ? product.id : void 0,
        size: {
          width: product.width,
          height: product.height,
          length: product.length
        },
        supplier_strid: packages.supplier_strid
      }))
    }))
  }
}

async function handleOk() {
  if (packageList.value?.length === 0) return createMessage.error('请从产品清单中添加新建包裹')
  try {
    const validResult = await handleBeforeSubmit()
    if (!validResult) return
    const mapHandlerOk = {
      add: handleAddOk,
      split: handleSplitOk
    }
    const data = getParams()
    mapHandlerOk[propsData.value.type]?.(data)
  } catch (err) {
    createMessage.error('创建失败！')
  }
}

function handleSplitOk(packages) {
  closeDrawer()
  handleClose()
  emits('split', { packages: packages.packageList })
}

async function handleAddOk(params) {
  try {
    const newparams = params.packageList.map((item) => {
      return {
        ...item,
      }
    })
    console.log(newparams)

    // changeLoading(true)
    // changeOkLoading(true)
    // const { message } = await addPackage(params)

    // emits('success')
    // createMessage.success(message)
    // closeDrawer()
    // handleClose()

    // changeLoading(false)
    // changeOkLoading(false)
  } catch (err) {
    createMessage.error('创建失败')
    changeLoading(false)
    changeOkLoading(false)
  }
}

function handleClose() {
  packageList.value = []
  selectProduct.value = []
  purchaseProductList.value = []
  packageFormEl.value = []
  productFormEl.value = {}
  curOrderIds.value = []
  pkgFlag.value = 1
  propsData.value = {}
}

// 同步信息到包裹中所有同类商品
function handleSyncPackageInfo(product) {
  // console.log(mapPackageProductForKey.value)
  const packageProduct = mapPackageProductForKey.value[product.key]
  if (!packageProduct) return
  const { length, width, height, material, code } = product
  for (const prod of packageProduct) {
    prod.length = length
    prod.width = width
    prod.height = height
    prod.material = material
    prod.code = code
  }
}

//
async function handleCountChange(val, product) {
  mapPackageProduct.value[product.itemKey].buildQuantity = val
  // const productForm = productFormEl.value[product.itemKey]
  // const { formActionType } = productForm
  // const { setFieldsValue } = formActionType
  // await setFieldsValue({ buildQuantity: val })
}

async function handleSyncInfoToPackages() {
  const syncList = packageList.value.filter((packages) => {
    return packages.product.length === 1 && packages.product[0].buildQuantity === 1
  })

  for (const packages of syncList) {
    const { length, width, height } = packages.product[0]
    packages.length = length
    packages.width = width
    packages.height = height
    // packages.volume = div(Math.floor(mul(mul(mul(length ?? 0, width ?? 0), height ?? 0), 10000)), 10000000000)
    packages.volume = div(mul(mul(length ?? 0, width ?? 0), height ?? 0), 1000000, 6)
    // const packageForm = mapPackageFormEl.value[packages.key]
    // const { formActionType } = packageForm
    // const { setFieldsValue } = formActionType
    // const { length, width, height } = packages.product[0]
    // await setFieldsValue({
    //   length,
    //   width,
    //   height,
    //   volume: div(Math.floor(mul(mul(mul(length ?? 0, width ?? 0), height ?? 0), 10000)), 10000000000)
    // })
  }
}

function setPackagesFormEl(el, idx, pkg) {
  if (el) {
    packageFormEl.value[idx] = { key: pkg.key, el }
  } else {
    packageFormEl.value.splice(idx, 1)
  }
}

function handleSplitGoods(goods) {
  const splitCount = splitGoodsCount.value ?? 0
  if (!splitCount) return createMessage.error('请输入数量')
  const useCount = sub(+goods.maxQuantity, compPackageProductCount.value[goods.key] ?? 0)
  if (useCount < 1 && splitCount > mul(useCount, 100)) {
    return createMessage.error('可使用数量小于1，最大填写数量为可使用数量*100')
  }

  if (useCount === 1 && splitCount > 100) return createMessage.error('对可用数量为1的商品不允许拆分超过100个包裹')

  const countArr = splitItems(useCount, splitCount, 2)

  for (const count of countArr) {
    packageList.value.push({
      ...(enableBaseInfo.value ? getFieldsValue() || {} : { length: 0.01, width: 0.01, height: 0.01 }),
      key: pkgFlag.value++,
      product: [{ ...cloneDeep(goods), buildQuantity: count, itemKey: random(1, 1000000000) }],
      projectNumber: goods.projectNumber
    })
  }

  showPopoverGather.value[goods.key] = false
}

/**
 * splitItems: 将第一个参数按第二个参数切分若干份，如果有余数则将余数加到最后一份中
 * @param {number} totalItems 总数
 * @param {number} numParts 切分份数
 * @param {number} acc 结果保留几位小数
 * @returns {array[]} 每一份对应的数值
 */
function splitItems(totalItems: number, numParts: number, acc: number) {
  console.log(totalItems, numParts, acc)

  // 计算每部分的基本数量，保留两位小数
  const baseValue = new Decimal(totalItems).div(numParts).toFixed(acc, Decimal.ROUND_DOWN)
  const baseNumber = parseFloat(baseValue)
  console.log(baseNumber)

  // 计算每部分总和和余数
  const totalBaseSum = new Decimal(baseNumber).mul(numParts - 1).toNumber()

  // 初始化结果数组
  const result = new Array(numParts).fill(baseNumber)
  console.log(result)

  // 调整最后一个元素以修正总和的误差
  result[numParts - 1] = new Decimal(totalItems).minus(totalBaseSum).toNumber() // totalItems - totalBaseSum

  return result
}

function handleShowSplitPopover(visible) {
  if (visible) {
    splitGoodsCount.value = null
  }
}

// 监听可使用数量，产品超过可使用数量，则输入数量=可使用数量
watch(
  () => compPackageProductCount.value,
  (val) => {
    for (const key of Object.keys(val)) {
      const product = mapProductList.value[key] || {}
      const useCount = sub(+product.maxQuantity, val[key] ?? 0)
      if (product.buildQuantity > useCount) {
        product.buildQuantity = useCount
      }
    }
  }
)
</script>

<style lang="less" scoped>
.wrap-title {
  .title {
    font-weight: 800;
    font-size: 16px;
    color: #383838;
  }
}

.package-product-wrap {
  padding: 15px;
  border-radius: 10px;

  &:last-child {
    margin-bottom: 0;
  }
  .package-name {
    font-weight: bold;
    font-size: 16px;
    color: #383838;
  }

  .product {
    display: flex;
    justify-content: space-between;
    .product-name {
      font-size: 14px;
      color: #27335b;
      font-weight: 600;
    }
    .product-count {
      font-weight: bold;
      font-size: 16px;
      //color: #27335b;
    }
  }
}

::v-deep(.checkbox-item) {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  border-top: 1px solid #fff;
  border-left: 1px solid #fff;
  border-right: 1px solid #fff;

  &.is-active {
    border: 1px solid #0960bd !important;
  }
  > span:not(.ant-checkbox) {
    display: inline-block;
    width: 100%;
  }
}

//::v-deep(.radio-item) {
//  border-radius: 10px;
//
//  > span:not(.ant-radio) {
//    display: block;
//    width: 100%;
//  }
//}

.product-info {
  .info-title {
    font-weight: 500;
    font-size: 14px;
    color: #27335b;
  }

  .info-desc {
    font-weight: 400;
    font-size: 14px;
    color: #777777;
  }

  .info-count {
    font-weight: bold;
    font-size: 18px;
    color: #27335b;
  }
}

::v-deep(.split-packages-input) {
  ::v-deep(.ant-input-number-group-wrapper) {
    width: 160px !important;
  }
}

.no-padding {
  ::v-deep(.scrollbar__wrap) {
    padding: 0 5px 0 0 !important;
  }
}

.form-wrap {
  ::v-deep(.ant-form-item) {
    margin-bottom: 0 !important;
  }
}
</style>
