<template>
  <div>
    <Card class="cart-item" hoverable :bodyStyle="{}">
      <!-- 标题部分start -->
      <template #title>
        <div class="title">
          <div class="title-item text-left">
            <div class="tit-tit">创建日期</div>
            <div class="date tit-content">{{ cardData.created_at }}</div>
          </div>
          <div class="flex flex-col items-end">
            <div class="tit-tit">收货号</div>
            <div class="no tit-content">{{ cardData.strid }}</div>
            <a-button v-if="cardData?.image" type="link" size="small" class="!p-0" @click="handleEmits('previewImg')"
              >查看水印图片</a-button
            >
          </div>
        </div>
      </template>
      <!-- 标题部分end -->

      <!-- 卡片主体部分start -->
      <div class="card-main">
        <div class="dept-bar">
          <div class="dept-left">
            <div>部门:{{ cardData.department_name }}</div>
          </div>
          <div class="dept-right">
            <Tag :color="mapStatus[cardData.status].color" style="font-size: 16px">{{ mapStatus[cardData.status].value }}</Tag>
          </div>
        </div>
        <div v-if="cardData?.doc_in_warehouse_header_strid" class="sales-bar">
          所属主入库单号:
          <!-- {{ getMapSalesWork[cardData.work?.basic_work_id]?.strid }} -->
          {{ cardData?.doc_in_warehouse_header_strid }}
        </div>
        <div v-if="cardData.work?.basic_work_id" class="sales-bar">
          所属销售单号:
          <!-- {{ getMapSalesWork[cardData.work?.basic_work_id]?.strid }} -->
          {{ cardData?.source_uniqid }}
        </div>
        <!-- 卡片内部表格部分start -->
        <Table :dataSource="cardData.item" :columns="columns" :pagination="false" size="small" :scroll="{ y: '100px', x: '100%' }" />
        <!-- 卡片内部表格部分end -->
      </div>
      <!-- 卡片主体部分end -->
      <div v-if="[2, 3].includes(cardData.status)" :class="['card-tips', { confirm: packageRight }]">
        <warning-filled v-if="!packageRight" style="color: #ff4d4f; margin-right: 10px" />
        <check-circle-filled v-else style="color: #02b624; margin-right: 10px" />
        <span class="tips-msg"> 待收包裹：{{ cardData.pkg_num || '-' }}； </span>
        <span v-if="!packageRight && hasPermission([71])">
          <a-button type="link" @click="handleEmits('editSaleNum', 1)">去修改</a-button>
        </span>
      </div>
      <div v-if="[2, 3].includes(cardData.status)" :class="['card-tips', { confirm: packageRight }]">
        <warning-filled v-if="!packageRight" style="color: #ff4d4f; margin-right: 10px" />
        <check-circle-filled v-else style="color: #02b624; margin-right: 10px" />
        <span class="tips-msg"> 已收包裹：{{ cardData.pkg_received || '-' }}； </span>
        <span v-if="!packageRight && hasPermission([71])">
          <a-button type="link" @click="handleEmits('editSaleNum', 2)">去修改</a-button>
        </span>
      </div>
      <template #actions v-if="pathname == '/sp/'">
        <span v-if="detailBtn" @click="handleEmits('detail')">详情</span>
        <span v-if="showEditBtn" @click="handleEmits('editClick')">编辑</span>
        <Tooltip v-else-if="disableEditBtn">
          <template #title>待收包裹数和已收包裹数不一致，无法进行编辑</template>
          编辑
        </Tooltip>
        <span v-if="[1, 2].includes(cardData.status)" @click="handleEmits('editSaleNum')"> 销售总数量</span>
        <span v-if="[1].includes(cardData.status) && hasPermission([72])" @click="handleEmits('editReceivedNum')">填写已收包裹</span>
        <span v-if="[2, 3].includes(cardData.status) && hasPermission([70])" @click="emits('getStocking', cardData)">查看实时库存</span>
        <span v-if="cardData.status === 0 && hasPermission([66])">
          <Popover trigger="click">
            <template #title>
              <span style="color: red">通过审核需要先填写待收包裹数</span>
            </template>
            <template #content>
              <InputNumber v-model:value="pkgNum" allowClear style="width: 100px !important" size="small" :min="0" :precision="0" />
              <Button type="primary" size="small" style="margin-left: 10px" @click="handleEmits('approve')">确定通过审核</Button>
            </template>
            <span>通过审核</span>
          </Popover>
        </span>
        <span v-if="cardData.status === 0 && hasPermission([63])">
          <Popconfirm title="删除后将无法恢复数据，确定删除吗" @confirm="handleEmits('delete')">删除</Popconfirm>
        </span>
      </template>
      <template #actions v-else>
        <span v-if="detailBtn" @click="handleEmits('detail')">详情</span>
      </template>
    </Card>
  </div>
</template>

<script setup lang="ts" name="WarehouseOrderCard">
import { Card, Table, Tag, Popover, Button, InputNumber, Popconfirm } from 'ant-design-vue'
import { WarningFilled, CheckCircleFilled } from '@ant-design/icons-vue'
import { mapStatus } from '../datas/datas'
// import { useMapStoreWithOut } from '/@/store/modules/commonMap'
import { computed, ref, unref, watch } from 'vue'
import { setPkgNum } from '/@/api/erp/inWarehouse'
import { useMessage } from '/@/hooks/web/useMessage'
import { usePermission } from '/@/hooks/web/usePermission'
import { useRoute } from 'vue-router'
// import { isNumber } from 'lodash-es'

const pathname = window.location.pathname

const route = useRoute()
const routeName = unref(route).name
const { hasPermission } = usePermission()
const packageRight = computed<boolean>(() => props.cardData.pkg_num === props.cardData.pkg_received)
const { createMessage } = useMessage()
const pkgNum = ref<number>(0)
// const { getMapSalesWork } = useMapStoreWithOut()
const columns = [
  // {
  //   title: '所属采购单',
  //   dataIndex: 'from_purchase',
  //   key: 'from_purchase',
  //   width: 250
  // },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: 100
  },
  {
    title: '要收到的商品数',
    dataIndex: 'qty_total',
    key: 'qty_total',
    width: 120
  },
  {
    title: '实际入库商品数',
    dataIndex: 'qty_received',
    key: 'qty_received',
    width: 100
  }
]

const props = withDefaults(
  defineProps<{
    cardData: Recordable
    date: string
  }>(),
  {}
)

watch(
  () => props.cardData,
  (val) => {
    pkgNum.value = val.pkg_num
  },
  { immediate: true, deep: true }
)

const detailBtn = computed<boolean>(
  () => (hasPermission([65]) && routeName === '/erp/inWarehouse') || (hasPermission([68]) && routeName === '/erp/inWarehouseNotice')
)

const showEditBtn = computed<boolean>(
  () =>
    ([2].includes(props.cardData.status) && packageRight.value && hasPermission([69])) ||
    ([0].includes(props.cardData.status) && hasPermission([64]))
)

const disableEditBtn = computed<boolean>(() => [2].includes(props.cardData.status) && !packageRight.value && hasPermission([69]))

const emits = defineEmits([
  'showImg',
  'editReceivedNum',
  'editSaleNum',
  'editClick',
  'getStocking',
  'approve',
  'delete',
  'detail',
  'previewImg'
])

type Tmodal = 'showImg' | 'editReceivedNum' | 'editSaleNum' | 'editClick' | 'approve' | 'delete' | 'detail' | 'previewImg'

async function handleEmits(model: Tmodal, SaleNum?: number) {
  if (model === 'approve') {
    try {
      if (pkgNum.value < 0) return createMessage.error('请输入包裹数！')
      const { msg } = await setPkgNum(props.cardData.id, pkgNum.value)
      if (msg === 'success') {
        emits(model)
      }
      return
    } catch (e) {
      throw new Error(`${e}`)
    }
  }
  if (model == 'editSaleNum') {
    return emits('editSaleNum', SaleNum)
  }
  emits(model)
}
</script>

<style scoped lang="less">
.cart-item {
  font-size: 14px;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .tit-content {
      font-size: 13px;
    }
    .date {
      color: #808080;
    }
    .no {
      color: #f81d22;
    }
  }
  .card-tips {
    display: flex;
    align-items: center;
    background: #ffe6e5;
    padding: 5px 10px;
    border-radius: 5px;
    margin-top: 10px;

    &.confirm {
      background: #deffe4;

      .tips-msg {
        color: #55d187;
      }
    }
    .tips-msg {
      text-align: left;
      color: #ff4d4f;
      flex: 1;
    }
  }
  .card-main {
    .sales-bar {
      text-align: left;
    }
    .dept-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .dept-left {
        flex: 1;
        text-align: left;
        white-space: pre-wrap;
        width: 65%;
        //flex-direction: column;
      }
      .dept-right {
        display: flex;
        justify-content: space-between;
        color: #1890ff;
        //width: 40%;
        .img:hover,
        .detail:hover {
          color: #0561b8;
        }
      }
    }
    .card-table {
      text-align: center;
      tr {
        td:last-child {
          max-width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .info {
      text-align: left;
      margin: 12px 0;
      .info-key-value {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .key {
          font-weight: 600;
        }
      }
    }
    .status-bar {
      color: #f81d22;
      font-size: 16px;
      font-weight: 600;
      text-align: right;
    }
    .status-bar.active {
      color: #87d068;
    }
    .download-btn {
      display: flex;
      justify-content: flex-start;
      font-size: 20px;

      .btn {
        color: #1890ff;
        &:hover {
          color: #0561b8;
        }
      }
    }
  }
}
</style>
